using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Configurations;

/// <summary>
/// File upload configuration options
/// </summary>
public class FileUploadOptions
{
    public const string SectionName = "FileUpload";

    /// <summary>
    /// Maximum file size in bytes (default: 50MB)
    /// </summary>
    [Range(1, long.MaxValue, ErrorMessage = "Maximum file size must be greater than 0")]
    public long MaxFileSize { get; set; } = 50 * 1024 * 1024; // 50MB

    /// <summary>
    /// Maximum number of files per bulk upload (default: 10)
    /// </summary>
    [Range(1, 100, ErrorMessage = "Maximum files per bulk upload must be between 1 and 100")]
    public int MaxFilesPerBulkUpload { get; set; } = 10;

    /// <summary>
    /// Allowed file extensions
    /// </summary>
    public string[] AllowedExtensions { get; set; } = 
    {
        ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
        ".txt", ".rtf", ".jpg", ".jpeg", ".png", ".gif", ".bmp",
        ".zip", ".rar", ".7z"
    };

    /// <summary>
    /// Allowed MIME types
    /// </summary>
    public string[] AllowedMimeTypes { get; set; } = 
    {
        // Documents
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-powerpoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "text/plain",
        "application/rtf",
        
        // Images
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/bmp",
        
        // Archives
        "application/zip",
        "application/x-rar-compressed",
        "application/x-7z-compressed"
    };

    /// <summary>
    /// Enable virus scanning (default: true)
    /// </summary>
    public bool EnableVirusScanning { get; set; } = true;

    /// <summary>
    /// Enable magic number validation (default: true)
    /// </summary>
    public bool EnableMagicNumberValidation { get; set; } = true;

    /// <summary>
    /// Maximum concurrent uploads (default: CPU count)
    /// </summary>
    [Range(1, 50, ErrorMessage = "Maximum concurrent uploads must be between 1 and 50")]
    public int MaxConcurrentUploads { get; set; } = Environment.ProcessorCount;

    /// <summary>
    /// Upload timeout in seconds (default: 300 seconds = 5 minutes)
    /// </summary>
    [Range(30, 3600, ErrorMessage = "Upload timeout must be between 30 seconds and 1 hour")]
    public int UploadTimeoutSeconds { get; set; } = 300;

    /// <summary>
    /// Enable progress tracking (default: true)
    /// </summary>
    public bool EnableProgressTracking { get; set; } = true;

    /// <summary>
    /// Temporary file retention hours (default: 24 hours)
    /// </summary>
    [Range(1, 168, ErrorMessage = "Temporary file retention must be between 1 hour and 1 week")]
    public int TempFileRetentionHours { get; set; } = 24;

    /// <summary>
    /// Rate limiting options
    /// </summary>
    public RateLimitOptions RateLimit { get; set; } = new();

    /// <summary>
    /// Check if file extension is allowed
    /// </summary>
    /// <param name="extension">File extension (with dot)</param>
    /// <returns>True if allowed</returns>
    public bool IsExtensionAllowed(string extension)
    {
        if (string.IsNullOrEmpty(extension))
            return false;

        return AllowedExtensions.Contains(extension.ToLowerInvariant());
    }

    /// <summary>
    /// Check if MIME type is allowed
    /// </summary>
    /// <param name="mimeType">MIME type</param>
    /// <returns>True if allowed</returns>
    public bool IsMimeTypeAllowed(string mimeType)
    {
        if (string.IsNullOrEmpty(mimeType))
            return false;

        return AllowedMimeTypes.Contains(mimeType.ToLowerInvariant());
    }

    /// <summary>
    /// Get human-readable file size
    /// </summary>
    /// <returns>Formatted file size</returns>
    public string GetMaxFileSizeFormatted()
    {
        const long kb = 1024;
        const long mb = kb * 1024;
        const long gb = mb * 1024;

        if (MaxFileSize >= gb)
            return $"{MaxFileSize / gb:F1} GB";
        if (MaxFileSize >= mb)
            return $"{MaxFileSize / mb:F1} MB";
        if (MaxFileSize >= kb)
            return $"{MaxFileSize / kb:F1} KB";
        
        return $"{MaxFileSize} bytes";
    }
}

/// <summary>
/// Rate limiting configuration
/// </summary>
public class RateLimitOptions
{
    /// <summary>
    /// Maximum uploads per minute per user (default: 10)
    /// </summary>
    [Range(1, 1000, ErrorMessage = "Uploads per minute must be between 1 and 1000")]
    public int UploadsPerMinute { get; set; } = 10;

    /// <summary>
    /// Maximum uploads per hour per user (default: 100)
    /// </summary>
    [Range(1, 10000, ErrorMessage = "Uploads per hour must be between 1 and 10000")]
    public int UploadsPerHour { get; set; } = 100;

    /// <summary>
    /// Maximum total upload size per hour per user in bytes (default: 500MB)
    /// </summary>
    [Range(1, long.MaxValue, ErrorMessage = "Upload size per hour must be greater than 0")]
    public long MaxUploadSizePerHour { get; set; } = 500 * 1024 * 1024; // 500MB
}

/// <summary>
/// File validation result
/// </summary>
public class FileValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public long FileSize { get; set; }
    public string? DetectedContentType { get; set; }
    public string? DetectedExtension { get; set; }
    public bool PassedVirusScan { get; set; } = true;
    public bool PassedMagicNumberCheck { get; set; } = true;

    public static FileValidationResult Success(long fileSize, string? contentType = null)
    {
        return new FileValidationResult
        {
            IsValid = true,
            FileSize = fileSize,
            DetectedContentType = contentType
        };
    }

    public static FileValidationResult Failure(params string[] errors)
    {
        return new FileValidationResult
        {
            IsValid = false,
            Errors = errors.ToList()
        };
    }

    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }
}
