using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace AcademicPerformance.Models.MongoDocuments;

/// <summary>
/// Simplified document structure for storing academician-submitted dynamic criterion data
/// </summary>
public class SubmittedDynamicDataDoc
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string? Id { get; set; } // Unique ID for this specific data entry instance

    [BsonElement("academicSubmissionId")]
    [BsonRequired]
    public required string AcademicSubmissionId { get; set; } // Public GUID Id of the parent AcademicSubmissionEntity

    [BsonElement("formCriterionLinkId")]
    [BsonRequired]
    public required string FormCriterionLinkId { get; set; } // Public GUID Id of the FormCriterionLinkEntity (Dynamic type)

    [BsonElement("academicianUniveristyUserId")]
    [BsonRequired]
    public required string AcademicianUniveristyUserId { get; set; }

    [BsonElement("data")]
    [BsonRequired]
    public required Dictionary<string, object> Data { get; set; } = new(); // Key: InputFieldDefinitionDoc.FieldId

    [BsonElement("createdAt")]
    [BsonRequired]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [BsonElement("updatedAt")]
    [BsonRequired]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}