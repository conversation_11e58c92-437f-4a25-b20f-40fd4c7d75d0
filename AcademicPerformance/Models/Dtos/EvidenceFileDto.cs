using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Controller review interface için evidence file bilgileri
    /// </summary>
    public class EvidenceFileDto
    {
        /// <summary>
        /// Evidence file ID'si (GUID)
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Dosya adı
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Orijinal dosya adı
        /// </summary>
        public string? OriginalFileName { get; set; }

        /// <summary>
        /// Dosya boyutu (bytes)
        /// </summary>
        public long SizeBytes { get; set; }

        /// <summary>
        /// Content type (MIME type)
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// Yüklenme tarihi
        /// </summary>
        public DateTime UploadedAt { get; set; }

        /// <summary>
        /// Yükleyen kullanıcı ID'si
        /// </summary>
        public string? UploadedByUserId { get; set; }

        /// <summary>
        /// Dosya a<PERSON>ıklaması
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Form criterion link ID'si
        /// </summary>
        public string? CriterionLinkId { get; set; }

        /// <summary>
        /// Criterion adı
        /// </summary>
        public string? CriterionName { get; set; }

        /// <summary>
        /// Field adı (hangi input field'ına ait)
        /// </summary>
        public string? FieldName { get; set; }

        /// <summary>
        /// Storage type (Local, MinIO, etc.)
        /// </summary>
        public string StorageType { get; set; } = "Local";

        /// <summary>
        /// MinIO bucket adı
        /// </summary>
        public string? MinioBucketName { get; set; }

        /// <summary>
        /// MinIO object adı
        /// </summary>
        public string? MinioObjectName { get; set; }

        /// <summary>
        /// File checksum (integrity kontrolü için)
        /// </summary>
        public string? FileChecksum { get; set; }

        /// <summary>
        /// Download URL'i (presigned URL olabilir)
        /// </summary>
        public string? DownloadUrl { get; set; }

        /// <summary>
        /// Preview URL'i (image/pdf preview için)
        /// </summary>
        public string? PreviewUrl { get; set; }

        /// <summary>
        /// Dosya preview edilebilir mi?
        /// </summary>
        public bool CanPreview { get; set; }

        /// <summary>
        /// Dosya download edilebilir mi?
        /// </summary>
        public bool CanDownload { get; set; } = true;

        /// <summary>
        /// Dosya silinebilir mi?
        /// </summary>
        public bool CanDelete { get; set; }

        /// <summary>
        /// Son değişiklik tarihi
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }

        /// <summary>
        /// Dosya durumu (Active, Deleted, etc.)
        /// </summary>
        public string Status { get; set; } = "Active";

        /// <summary>
        /// Dosya kategorisi (Evidence, Supporting, etc.)
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// Dosya etiketleri
        /// </summary>
        public List<string>? Tags { get; set; }

        /// <summary>
        /// Dosya metadata'sı (JSON format)
        /// </summary>
        public string? Metadata { get; set; }
    }

    /// <summary>
    /// Evidence file upload DTO'su
    /// </summary>
    public class EvidenceFileUploadDto
    {
        /// <summary>
        /// Yüklenecek dosya
        /// </summary>
        [Required]
        public IFormFile File { get; set; } = null!;

        /// <summary>
        /// Dosya açıklaması
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Criterion link ID'si
        /// </summary>
        public string? CriterionLinkId { get; set; }

        /// <summary>
        /// Field adı
        /// </summary>
        public string? FieldName { get; set; }

        /// <summary>
        /// Dosya kategorisi
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// Dosya etiketleri
        /// </summary>
        public List<string>? Tags { get; set; }
    }

    /// <summary>
    /// Evidence file update DTO'su
    /// </summary>
    public class EvidenceFileUpdateDto
    {
        /// <summary>
        /// Evidence file ID'si
        /// </summary>
        [Required]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Yeni dosya açıklaması
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Yeni dosya kategorisi
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// Yeni dosya etiketleri
        /// </summary>
        public List<string>? Tags { get; set; }
    }

    /// <summary>
    /// Evidence file download response DTO'su
    /// </summary>
    public class EvidenceFileDownloadDto
    {
        /// <summary>
        /// Dosya stream'i
        /// </summary>
        public Stream FileStream { get; set; } = null!;

        /// <summary>
        /// Dosya adı
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Content type
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// Dosya boyutu
        /// </summary>
        public long SizeBytes { get; set; }
    }
}
