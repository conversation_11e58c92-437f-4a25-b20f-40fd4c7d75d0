namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Submission ana DTO'su - API response için
    /// </summary>
    public class SubmissionDto
    {
        public string Id { get; set; } = string.Empty;
        public string FormId { get; set; } = string.Empty;
        public string AcademicianUserId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? SubmittedAt { get; set; }
        public List<SubmissionCriterionDataDto> CriteriaData { get; set; } = new();
        public decimal CompletionPercentage { get; set; }
        public string? Notes { get; set; }
        public DateTime LastActivityAt { get; set; }
    }

    /// <summary>
    /// Submission oluşturma DTO'su
    /// </summary>
    public class SubmissionCreateDto
    {
        public string FormId { get; set; } = string.Empty;
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Submission güncelleme DTO'su
    /// </summary>
    public class SubmissionUpdateDto
    {
        public string Id { get; set; } = string.Empty;
        public List<SubmissionCriterionDataDto> CriteriaData { get; set; } = new();
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Kriter data DTO'su
    /// </summary>
    public class SubmissionCriterionDataDto
    {
        public string CriterionLinkId { get; set; } = string.Empty;
        public string CriterionType { get; set; } = string.Empty;
        public string CriterionName { get; set; } = string.Empty;
        public List<CriterionDataEntryDto> DataEntries { get; set; } = new();
        public bool IsCompleted { get; set; }
        public DateTime? LastUpdated { get; set; }
        public string? Notes { get; set; }
        public decimal? CalculatedScore { get; set; }
    }

    /// <summary>
    /// Kriter data entry DTO'su
    /// </summary>
    public class CriterionDataEntryDto
    {
        public string Id { get; set; } = string.Empty;
        public string FieldName { get; set; } = string.Empty;
        public string FieldType { get; set; } = string.Empty;
        public string? Value { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public string? FileId { get; set; }
        public bool IsValid { get; set; } = true;
        public string? ValidationError { get; set; }
    }

    /// <summary>
    /// Kriter data input DTO'su - Veri girişi için
    /// </summary>
    public class CriterionDataInputDto
    {
        public List<CriterionDataEntryDto> DataEntries { get; set; } = new();
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Kriter data güncelleme DTO'su
    /// </summary>
    public class CriterionDataUpdateDto
    {
        public string FieldName { get; set; } = string.Empty;
        public string? Value { get; set; }
        public string? Description { get; set; }
    }

    /// <summary>
    /// Submission status güncelleme DTO'su
    /// </summary>
    public class SubmissionStatusUpdateDto
    {
        public string Id { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Submission özet DTO'su - Dashboard için
    /// </summary>
    public class SubmissionSummaryDto
    {
        public string Id { get; set; } = string.Empty;
        public string FormId { get; set; } = string.Empty;
        public string FormTitle { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public decimal CompletionPercentage { get; set; }
        public DateTime? SubmissionDeadline { get; set; }
        public DateTime LastActivityAt { get; set; }
        public int TotalCriteria { get; set; }
        public int CompletedCriteria { get; set; }
        public bool IsOverdue { get; set; }
    }

    /// <summary>
    /// File-criterion integration için kullanılır
    /// </summary>
    public class SubmissionFileDto
    {
        /// <summary>
        /// Evidence file ID'si
        /// </summary>
        public string FileId { get; set; } = string.Empty;

        /// <summary>
        /// Field adı (örn: "EvidenceFile", "SupportingDocument")
        /// </summary>
        public string FieldName { get; set; } = string.Empty;

        /// <summary>
        /// Criterion link ID'si
        /// </summary>
        public string CriterionLinkId { get; set; } = string.Empty;

        /// <summary>
        /// Criterion adı
        /// </summary>
        public string CriterionName { get; set; } = string.Empty;

        /// <summary>
        /// Dosya açıklaması
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Yüklenme tarihi
        /// </summary>
        public DateTime UploadedAt { get; set; }

        /// <summary>
        /// Son değişiklik tarihi
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }

        /// <summary>
        /// Dosya adı (EvidenceFileEntity'den gelecek)
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// Dosya boyutu (bytes)
        /// </summary>
        public long? SizeBytes { get; set; }

        /// <summary>
        /// Content type
        /// </summary>
        public string? ContentType { get; set; }

        /// <summary>
        /// Download URL'i
        /// </summary>
        public string? DownloadUrl { get; set; }
    }
}
