using AcademicPerformance.Models.AcademicPerformanceDbContextModels;

namespace AcademicPerformance.Interfaces;

/// <summary>
/// Evidence file data access interface
/// </summary>
public interface IEvidenceFileStore
{
    /// <summary>
    /// Create a new evidence file record
    /// </summary>
    /// <param name="evidenceFile">Evidence file entity</param>
    /// <returns>Created evidence file</returns>
    Task<EvidenceFileEntity> CreateEvidenceFileAsync(EvidenceFileEntity evidenceFile);

    /// <summary>
    /// Get evidence file by ID
    /// </summary>
    /// <param name="fileId">File ID</param>
    /// <returns>Evidence file or null</returns>
    Task<EvidenceFileEntity?> GetEvidenceFileByIdAsync(string fileId);

    /// <summary>
    /// Get evidence files by submission ID
    /// </summary>
    /// <param name="submissionId">Academic submission ID</param>
    /// <returns>List of evidence files</returns>
    Task<List<EvidenceFileEntity>> GetEvidenceFilesBySubmissionIdAsync(int submissionId);

    /// <summary>
    /// Get evidence files by criterion link ID
    /// </summary>
    /// <param name="criterionLinkId">Form criterion link ID</param>
    /// <returns>List of evidence files</returns>
    Task<List<EvidenceFileEntity>> GetEvidenceFilesByCriterionLinkIdAsync(string criterionLinkId);

    /// <summary>
    /// Update evidence file
    /// </summary>
    /// <param name="evidenceFile">Evidence file entity</param>
    /// <returns>True if updated successfully</returns>
    Task<bool> UpdateEvidenceFileAsync(EvidenceFileEntity evidenceFile);

    /// <summary>
    /// Delete evidence file
    /// </summary>
    /// <param name="fileId">File ID</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteEvidenceFileAsync(string fileId);

    /// <summary>
    /// Get evidence files by MinIO object name
    /// </summary>
    /// <param name="objectName">MinIO object name</param>
    /// <returns>Evidence file or null</returns>
    Task<EvidenceFileEntity?> GetEvidenceFileByObjectNameAsync(string objectName);

    /// <summary>
    /// Create multiple evidence files in batch
    /// </summary>
    /// <param name="evidenceFiles">List of evidence files</param>
    /// <returns>List of created evidence files</returns>
    Task<List<EvidenceFileEntity>> CreateEvidenceFilesBatchAsync(List<EvidenceFileEntity> evidenceFiles);

    /// <summary>
    /// Get evidence files by user ID
    /// </summary>
    /// <param name="userId">University user ID</param>
    /// <returns>List of evidence files</returns>
    Task<List<EvidenceFileEntity>> GetEvidenceFilesByUserIdAsync(string userId);

    /// <summary>
    /// Get evidence files by date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>List of evidence files</returns>
    Task<List<EvidenceFileEntity>> GetEvidenceFilesByDateRangeAsync(DateTime startDate, DateTime endDate);
}
