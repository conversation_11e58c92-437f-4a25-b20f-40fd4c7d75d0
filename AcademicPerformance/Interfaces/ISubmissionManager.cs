using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Submission management interface - Business logic katmanı
    /// </summary>
    public interface ISubmissionManager
    {
        #region Submission Management

        /// <summary>
        /// Akademisyenin belirli bir form için submission'ını getir
        /// </summary>
        Task<SubmissionDto?> GetSubmissionByFormIdAsync(string universityUserId, string formId);

        /// <summary>
        /// Akademisyenin submission'ını ID ile getir
        /// </summary>
        Task<SubmissionDto?> GetSubmissionByIdAsync(string universityUserId, string submissionId);

        /// <summary>
        /// Yeni submission oluştur
        /// </summary>
        Task<SubmissionDto> CreateSubmissionAsync(string universityUserId, SubmissionCreateDto dto);

        /// <summary>
        /// Submission'ı güncelle (draft save)
        /// </summary>
        Task<bool> UpdateSubmissionAsync(string universityUserId, SubmissionUpdateDto dto);

        /// <summary>
        /// Submission'ı sil
        /// </summary>
        Task<bool> DeleteSubmissionAsync(string universityUserId, string submissionId);

        /// <summary>
        /// Submission status'unu güncelle
        /// </summary>
        Task<bool> UpdateSubmissionStatusAsync(string universityUserId, SubmissionStatusUpdateDto dto);

        #endregion

        #region Criterion Data Management

        /// <summary>
        /// Belirli bir kriter için performance data girişi yap
        /// </summary>
        Task<bool> InputCriterionDataAsync(string universityUserId, string formId, string criterionLinkId, CriterionDataInputDto dto);

        /// <summary>
        /// Belirli bir kriter data entry'sini güncelle
        /// </summary>
        Task<bool> UpdateCriterionDataAsync(string universityUserId, string formId, string criterionLinkId, string dataId, CriterionDataUpdateDto dto);

        /// <summary>
        /// Belirli bir kriter data entry'sini sil
        /// </summary>
        Task<bool> DeleteCriterionDataAsync(string universityUserId, string formId, string criterionLinkId, string dataId);

        /// <summary>
        /// Kriter için tüm data entry'leri getir
        /// </summary>
        Task<List<CriterionDataEntryDto>> GetCriterionDataEntriesAsync(string universityUserId, string formId, string criterionLinkId);

        /// <summary>
        /// Kriter için tüm data entry'leri sayfalanmış olarak getir
        /// </summary>
        Task<PagedListDto<CriterionDataEntryDto>> GetCriterionDataEntriesAsync(string universityUserId, string formId, string criterionLinkId, PagedListCo<GetStatusFilterCo> co);

        #endregion

        #region Validation and Calculation

        /// <summary>
        /// Submission'ın tamamlanma yüzdesini hesapla
        /// </summary>
        Task<decimal> CalculateCompletionPercentageAsync(string submissionId);

        /// <summary>
        /// Kriter data girişini validate et
        /// </summary>
        Task<bool> ValidateCriterionDataAsync(string criterionLinkId, CriterionDataInputDto dto);

        /// <summary>
        /// Submission'ın submit edilebilir olup olmadığını kontrol et
        /// </summary>
        Task<bool> CanSubmitAsync(string universityUserId, string submissionId);

        /// <summary>
        /// Akademisyenin form'a erişim yetkisi var mı kontrol et
        /// </summary>
        Task<bool> CanAcademicianAccessFormAsync(string universityUserId, string formId);

        #endregion

        #region Advanced Search Operations

        /// <summary>
        /// Gelişmiş filtreleme kriterleri ile submission'ları sayfalanmış olarak getir
        /// </summary>
        /// <param name="universityUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="co">Sayfalama ve filtreleme kriterleri</param>
        /// <returns>Sayfalanmış submission listesi</returns>
        Task<PagedListDto<SubmissionDto>> GetSubmissionsWithAdvancedFilteringAsync(string universityUserId, PagedListCo<GetAcademicSubmissionsCo> co);

        #endregion

        #region Helper Methods

        /// <summary>
        /// Submission'ı getir veya yoksa oluştur
        /// </summary>
        Task<SubmissionDto> GetOrCreateSubmissionAsync(string universityUserId, string formId);

        /// <summary>
        /// Akademisyenin tüm submission'larını getir
        /// </summary>
        Task<List<SubmissionSummaryDto>> GetAcademicianSubmissionsAsync(string universityUserId);

        /// <summary>
        /// Form için toplam kriter sayısını getir
        /// </summary>
        Task<int> GetTotalCriteriaCountAsync(string formId);

        #endregion



        /// <summary>
        /// Dosyayı submission'a ve criterion'a bağla
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="criterionLinkId">Criterion Link ID'si</param>
        /// <param name="evidenceFileId">Evidence File ID'si</param>
        /// <param name="fieldName">Field adı (opsiyonel)</param>
        /// <returns>Bağlantı başarılı mı?</returns>
        Task<bool> AttachFileToSubmissionAsync(string submissionId, string criterionLinkId, string evidenceFileId, string? fieldName = null);

        /// <summary>
        /// Submission'dan dosyayı kaldır
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="criterionLinkId">Criterion Link ID'si</param>
        /// <param name="evidenceFileId">Evidence File ID'si</param>
        /// <returns>Kaldırma başarılı mı?</returns>
        Task<bool> DetachFileFromSubmissionAsync(string submissionId, string criterionLinkId, string evidenceFileId);

        /// <summary>
        /// Submission'ın tüm dosyalarını getir
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <returns>Dosya listesi</returns>
        Task<List<SubmissionFileDto>> GetSubmissionFilesAsync(string submissionId);


    }
}
