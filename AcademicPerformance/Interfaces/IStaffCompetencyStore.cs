using AcademicPerformance.Extensions;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Personel yetkinlik değerlendirme veri katmanı interface'i
    /// </summary>
    public interface IStaffCompetencyStore
    {
        #region CRUD Operations

        /// <summary>
        /// Personel yetkinlik değerlendirmesi oluştur
        /// </summary>
        /// <param name="entity">Oluşturulacak entity</param>
        /// <returns>Oluşturulan entity</returns>
        Task<StaffCompetencyEvaluationEntity> CreateStaffCompetencyAsync(StaffCompetencyEvaluationEntity entity);

        /// <summary>
        /// Personel yetkinlik değerlendirmesini güncelle
        /// </summary>
        /// <param name="entity">Güncellenecek entity</param>
        /// <returns>Güncelleme başarılı mı</returns>
        Task<bool> UpdateStaffCompetencyAsync(StaffCompetencyEvaluationEntity entity);

        /// <summary>
        /// Personel yetkinlik değerlendirmesini sil (soft delete)
        /// </summary>
        /// <param name="id">Kayıt ID'si</param>
        /// <param name="deletedByUserId">Silen kullanıcı ID'si</param>
        /// <returns>Silme başarılı mı</returns>
        Task<bool> DeleteStaffCompetencyAsync(string id, string deletedByUserId);

        /// <summary>
        /// Personel yetkinlik değerlendirmesini getir
        /// </summary>
        /// <param name="id">Kayıt ID'si</param>
        /// <returns>Entity</returns>
        Task<StaffCompetencyEvaluationEntity?> GetStaffCompetencyAsync(string id);

        /// <summary>
        /// Personel yetkinlik değerlendirmelerini filtreli listele
        /// </summary>
        /// <param name="co">Filtreleme ve sayfalama parametreleri</param>
        /// <returns>Sayfalanmış entity listesi</returns>
        Task<PagedListDto<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesAsync(
            PagedListCo<StaffCompetencyFilterDto> co);

        #endregion

        #region Query Operations

        /// <summary>
        /// Personel için yetkinlik değerlendirmelerini getir
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="period">Dönem (opsiyonel)</param>
        /// <param name="limit">Limit (opsiyonel)</param>
        /// <returns>Yetkinlik değerlendirmeleri</returns>
        Task<List<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesByStaffIdAsync(
            string staffId,
            string? period = null,
            int? limit = null);

        /// <summary>
        /// Bölüm için yetkinlik değerlendirmelerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Yetkinlik değerlendirmeleri</returns>
        Task<List<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesByDepartmentAsync(
            string departmentId,
            string period);

        /// <summary>
        /// Değerlendirici için yetkinlik değerlendirmelerini getir
        /// </summary>
        /// <param name="evaluatorId">Değerlendirici ID'si</param>
        /// <param name="period">Dönem (opsiyonel)</param>
        /// <returns>Yetkinlik değerlendirmeleri</returns>
        Task<List<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesByEvaluatorAsync(
            string evaluatorId,
            string? period = null);

        /// <summary>
        /// Durum bazında yetkinlik değerlendirmelerini getir
        /// </summary>
        /// <param name="status">Değerlendirme durumu</param>
        /// <param name="departmentId">Bölüm ID'si (opsiyonel)</param>
        /// <returns>Yetkinlik değerlendirmeleri</returns>
        Task<List<StaffCompetencyEvaluationEntity>> GetStaffCompetenciesByStatusAsync(
            string status,
            string? departmentId = null);

        #endregion

        #region Statistical Operations

        /// <summary>
        /// Bölüm yetkinlik istatistiklerini hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>İstatistik verileri</returns>
        Task<List<CompetencyAreaSummaryDto>> CalculateDepartmentCompetencyStatisticsAsync(
            string departmentId,
            string period);

        /// <summary>
        /// Personel yetkinlik trend verilerini getir
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="periodCount">Dönem sayısı</param>
        /// <returns>Trend verileri</returns>
        Task<List<CompetencyTrendDataDto>> GetStaffCompetencyTrendDataAsync(
            string staffId,
            int periodCount);

        /// <summary>
        /// Yetkinlik benchmark verilerini hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Benchmark verileri</returns>
        Task<CompetencyBenchmarkDto> CalculateCompetencyBenchmarkAsync(
            string departmentId,
            string period);

        /// <summary>
        /// Yetkinlik alanı ortalama skorlarını hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <param name="competencyArea">Yetkinlik alanı (opsiyonel)</param>
        /// <returns>Ortalama skorlar</returns>
        Task<Dictionary<string, double>> CalculateAverageCompetencyScoresAsync(
            string departmentId,
            string period,
            string? competencyArea = null);

        #endregion

        #region Aggregation Operations

        /// <summary>
        /// Personel genel yetkinlik skorunu hesapla
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Genel yetkinlik skoru</returns>
        Task<double> CalculateOverallCompetencyScoreAsync(string staffId, string period);

        /// <summary>
        /// Yetkinlik alanı skorunu hesapla
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="competencyArea">Yetkinlik alanı</param>
        /// <param name="period">Dönem</param>
        /// <returns>Yetkinlik alanı skoru</returns>
        Task<double> CalculateCompetencyAreaScoreAsync(
            string staffId,
            string competencyArea,
            string period);

        /// <summary>
        /// Bölüm yetkinlik dağılımını hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Performans dağılımı</returns>
        Task<Dictionary<string, int>> CalculateDepartmentCompetencyDistributionAsync(
            string departmentId,
            string period);

        /// <summary>
        /// Yetkinlik büyüme oranını hesapla
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="currentPeriod">Mevcut dönem</param>
        /// <param name="previousPeriod">Önceki dönem</param>
        /// <returns>Büyüme oranı</returns>
        Task<double> CalculateCompetencyGrowthRateAsync(
            string staffId,
            string currentPeriod,
            string previousPeriod);

        #endregion

        #region Search Operations

        /// <summary>
        /// Yetkinlik değerlendirmelerinde arama yap
        /// </summary>
        /// <param name="searchTerm">Arama terimi</param>
        /// <param name="departmentId">Bölüm ID'si (opsiyonel)</param>
        /// <param name="period">Dönem (opsiyonel)</param>
        /// <returns>Arama sonuçları</returns>
        Task<List<StaffCompetencyEvaluationEntity>> SearchStaffCompetenciesAsync(
            string searchTerm,
            string? departmentId = null,
            string? period = null);

        /// <summary>
        /// Yetkinlik skoruna göre personel ara
        /// </summary>
        /// <param name="minScore">Minimum skor</param>
        /// <param name="maxScore">Maksimum skor</param>
        /// <param name="competencyArea">Yetkinlik alanı (opsiyonel)</param>
        /// <param name="departmentId">Bölüm ID'si (opsiyonel)</param>
        /// <param name="period">Dönem</param>
        /// <returns>Arama sonuçları</returns>
        Task<List<StaffCompetencyEvaluationEntity>> SearchByCompetencyScoreAsync(
            double minScore,
            double maxScore,
            string? competencyArea = null,
            string? departmentId = null,
            string period = "");

        /// <summary>
        /// En yüksek performanslı personeli getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <param name="competencyArea">Yetkinlik alanı (opsiyonel)</param>
        /// <param name="limit">Limit</param>
        /// <returns>En yüksek performanslı personel listesi</returns>
        Task<List<StaffCompetencyEvaluationEntity>> GetTopPerformersAsync(
            string departmentId,
            string period,
            string? competencyArea = null,
            int limit = 10);

        #endregion

        #region Validation Operations

        /// <summary>
        /// Personel yetkinlik verilerini doğrula
        /// </summary>
        /// <param name="entity">Doğrulanacak entity</param>
        /// <returns>Doğrulama sonucu</returns>
        Task<ValidationResultDto> ValidateStaffCompetencyAsync(StaffCompetencyEvaluationEntity entity);

        /// <summary>
        /// Duplicate yetkinlik değerlendirmesi kontrolü
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="period">Dönem</param>
        /// <param name="evaluatorId">Değerlendirici ID'si</param>
        /// <param name="excludeId">Hariç tutulacak ID (güncelleme için)</param>
        /// <returns>Duplicate var mı</returns>
        Task<bool> CheckDuplicateEvaluationAsync(
            string staffId,
            string period,
            string evaluatorId,
            string? excludeId = null);

        /// <summary>
        /// Yetkinlik skorlarının geçerliliğini kontrol et
        /// </summary>
        /// <param name="entity">Kontrol edilecek entity</param>
        /// <returns>Geçerli mi</returns>
        Task<bool> ValidateCompetencyScoresAsync(StaffCompetencyEvaluationEntity entity);

        #endregion

        #region Utility Operations

        /// <summary>
        /// Personel yetkinlik verilerini senkronize et
        /// </summary>
        /// <param name="staffId">Personel ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Senkronizasyon başarılı mı</returns>
        Task<bool> SynchronizeStaffCompetencyDataAsync(string staffId, string period);

        /// <summary>
        /// Yetkinlik değerlendirme sayısını getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si (opsiyonel)</param>
        /// <param name="period">Dönem (opsiyonel)</param>
        /// <param name="status">Durum (opsiyonel)</param>
        /// <returns>Toplam sayı</returns>
        Task<int> GetStaffCompetencyCountAsync(
            string? departmentId = null,
            string? period = null,
            string? status = null);

        /// <summary>
        /// Yetkinlik değerlendirme durumlarını toplu güncelle - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="ids">Güncellenecek ID'ler</param>
        /// <param name="newStatus">Yeni durum</param>
        /// <param name="updatedByUserId">Güncelleyen kullanıcı ID'si</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> BulkUpdateStatusAsync(List<string> ids, string newStatus, string updatedByUserId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Eski yetkinlik değerlendirmelerini arşivle - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="cutoffDate">Kesim tarihi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> ArchiveOldEvaluationsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default);

        /// <summary>
        /// Staff competency evaluations bulk insert - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="entities">Entity listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> BulkInsertAsync(List<StaffCompetencyEvaluationEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Staff competency evaluations bulk delete - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="entities">Entity listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> BulkDeleteAsync(List<StaffCompetencyEvaluationEntity> entities, CancellationToken cancellationToken = default);

        #endregion
    }
}
