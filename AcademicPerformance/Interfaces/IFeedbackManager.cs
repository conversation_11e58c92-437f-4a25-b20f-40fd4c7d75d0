using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Submission approval/rejection feedback, revision request ve feedback history işlemleri için gerekli operasyonları sağlar
    /// </summary>
    public interface IFeedbackManager
    {
        #region Feedback History Operations

        /// <summary>
        /// Submission için tüm feedback geçmişini getir
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="requestingUserId">İsteği yapan kullanıcı ID'si (authorization için)</param>
        /// <returns>Feedback geçmişi</returns>
        Task<FeedbackHistoryDto> GetFeedbackHistoryAsync(string submissionId, string requestingUserId);

        /// <summary>
        /// Akademisyen için tüm submission'ların feedback özetini getir
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış feedback özet listesi</returns>
        Task<PagedListDto<FeedbackSummaryDto>> GetAcademicianFeedbackSummaryAsync(string academicianUserId, PagedListCo<FeedbackFilterCo> co);

        /// <summary>
        /// Controller için verdiği feedback'lerin özetini getir
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Controller feedback istatistikleri</returns>
        Task<PagedListDto<FeedbackSummaryDto>> GetControllerFeedbackSummaryAsync(string controllerId, PagedListCo<FeedbackFilterCo> co);

        #endregion

        #region Approval Feedback Operations

        /// <summary>
        /// Gelişmiş approval feedback oluştur
        /// Kriter bazında değerlendirme ve rating sistemi ile
        /// </summary>
        /// <param name="feedbackDto">Approval feedback verileri</param>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <returns>Oluşturulan feedback entry</returns>
        Task<FeedbackEntryDto> CreateApprovalFeedbackAsync(ApprovalFeedbackDto feedbackDto, string controllerId);

        /// <summary>
        /// Approval feedback'i güncelle
        /// </summary>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <param name="feedbackDto">Güncellenmiş feedback verileri</param>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> UpdateApprovalFeedbackAsync(string feedbackId, ApprovalFeedbackDto feedbackDto, string controllerId);

        #endregion

        #region Revision Request Operations

        /// <summary>
        /// Revision request oluştur
        /// Kriter bazında düzeltme talepleri ile
        /// </summary>
        /// <param name="revisionDto">Revision request verileri</param>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <returns>Oluşturulan revision request</returns>
        Task<FeedbackEntryDto> CreateRevisionRequestAsync(RevisionRequestDto revisionDto, string controllerId);

        /// <summary>
        /// Revision request'i güncelle
        /// </summary>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <param name="revisionDto">Güncellenmiş revision request verileri</param>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> UpdateRevisionRequestAsync(string feedbackId, RevisionRequestDto revisionDto, string controllerId);

        /// <summary>
        /// Revision request'e akademisyen yanıtını kaydet
        /// </summary>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <param name="academicianResponse">Akademisyen yanıtı</param>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <returns>Yanıt kaydedildi mi?</returns>
        Task<bool> RespondToRevisionRequestAsync(string feedbackId, string academicianResponse, string academicianUserId);

        /// <summary>
        /// Aktif revision request'leri getir
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <returns>Aktif revision request'ler</returns>
        Task<List<FeedbackEntryDto>> GetActiveRevisionRequestsAsync(string academicianUserId);

        /// <summary>
        /// Pending revision requests listesi (controller için)
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="co">Pagination ve filter criteria</param>
        /// <returns>Pending revision requests</returns>
        Task<PagedListDto<FeedbackEntryDto>> GetPendingRevisionRequestsAsync(string controllerId, PagedListCo<GetFeedbackFilterCo> co);

        /// <summary>
        /// Deadline yaklaşan revision requests
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="daysThreshold">Kaç gün kala uyarı verilecek</param>
        /// <returns>Deadline yaklaşan revision requests</returns>
        Task<List<FeedbackEntryDto>> GetUpcomingRevisionDeadlinesAsync(string controllerId, int daysThreshold);

        #endregion

        #region Rejection Feedback Operations

        /// <summary>
        /// Rejection feedback oluştur (mevcut SubmissionRejectionDto ile uyumlu)
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="comments">Rejection yorumları</param>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <returns>Oluşturulan rejection feedback</returns>
        Task<FeedbackEntryDto> CreateRejectionFeedbackAsync(string submissionId, string comments, string controllerId);

        #endregion

        #region Validation and Authorization

        /// <summary>
        /// Kullanıcının feedback oluşturma yetkisi var mı kontrol et
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="feedbackType">Feedback türü</param>
        /// <returns>Yetki var mı?</returns>
        Task<bool> CanCreateFeedbackAsync(string userId, string submissionId, string feedbackType);

        /// <summary>
        /// Kullanıcının feedback görüntüleme yetkisi var mı kontrol et
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="submissionId">Submission ID'si</param>
        /// <returns>Yetki var mı?</returns>
        Task<bool> CanViewFeedbackAsync(string userId, string submissionId);

        /// <summary>
        /// Feedback'in düzenlenebilir olup olmadığını kontrol et
        /// </summary>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <returns>Düzenlenebilir mi?</returns>
        Task<bool> CanEditFeedbackAsync(string feedbackId, string userId);

        /// <summary>
        /// Submission'ın feedback alabilir durumda olup olmadığını kontrol et
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="feedbackType">Feedback türü</param>
        /// <returns>Feedback alabilir mi?</returns>
        Task<bool> CanReceiveFeedbackAsync(string submissionId, string feedbackType);

        #endregion

        #region Statistics and Analytics

        /// <summary>
        /// Feedback istatistiklerini getir (dashboard için)
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="userRole">Kullanıcı rolü (Academician/Controller)</param>
        /// <param name="dateRange">Tarih aralığı (opsiyonel)</param>
        /// <returns>Feedback istatistikleri</returns>
        Task<FeedbackSummaryDto> GetFeedbackStatisticsAsync(string userId, string userRole, DateRange? dateRange = null);

        /// <summary>
        /// Ortalama feedback süresini hesapla
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si (opsiyonel)</param>
        /// <param name="dateRange">Tarih aralığı (opsiyonel)</param>
        /// <returns>Ortalama feedback süresi (saat)</returns>
        Task<double> CalculateAverageFeedbackTimeAsync(string? controllerId = null, DateRange? dateRange = null);

        /// <summary>
        /// En çok revision talep edilen kriterleri getir
        /// </summary>
        /// <param name="limit">Sonuç limiti</param>
        /// <param name="dateRange">Tarih aralığı (opsiyonel)</param>
        /// <returns>Kriter listesi ve revision sayıları</returns>
        Task<List<CriterionRevisionStatDto>> GetMostRevisedCriteriaAsync(int limit = 10, DateRange? dateRange = null);

        #endregion

        #region Notification and Communication

        /// <summary>
        /// Feedback notification gönder
        /// </summary>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <param name="notificationType">Notification türü</param>
        /// <returns>Notification gönderildi mi?</returns>
        Task<bool> SendFeedbackNotificationAsync(string feedbackId, string notificationType);

        /// <summary>
        /// Deadline yaklaşan revision request'ler için reminder gönder
        /// </summary>
        /// <param name="daysBeforeDeadline">Deadline'dan kaç gün önce</param>
        /// <returns>Gönderilen reminder sayısı</returns>
        Task<int> SendRevisionDeadlineRemindersAsync(int daysBeforeDeadline = 3);

        #endregion

        #region Helper Methods

        /// <summary>
        /// Feedback türünün geçerli olup olmadığını kontrol et
        /// </summary>
        /// <param name="feedbackType">Feedback türü</param>
        /// <returns>Geçerli mi?</returns>
        Task<bool> IsValidFeedbackTypeAsync(string feedbackType);

        /// <summary>
        /// Submission status'una göre izin verilen feedback türlerini getir
        /// </summary>
        /// <param name="submissionStatus">Submission status'u</param>
        /// <returns>İzin verilen feedback türleri</returns>
        Task<List<string>> GetAllowedFeedbackTypesAsync(string submissionStatus);

        #endregion
    }


}
