using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Feedback Store interface - PostgreSQL operations
    /// </summary>
    public interface IFeedbackStore
    {
        #region Submission Feedback CRUD Operations

        /// <summary>
        /// Yeni feedback oluştur
        /// </summary>
        /// <param name="feedbackEntity">Feedback entity</param>
        /// <returns>Oluşturulan feedback entity</returns>
        Task<SubmissionFeedbackEntity> CreateFeedbackAsync(SubmissionFeedbackEntity feedbackEntity);

        /// <summary>
        /// Feedback'i ID ile getir
        /// </summary>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <returns>Feedback entity</returns>
        Task<SubmissionFeedbackEntity?> GetFeedbackByIdAsync(string feedbackId);

        /// <summary>
        /// Feedback'i güncelle
        /// </summary>
        /// <param name="feedbackEntity">Güncellenmiş feedback entity</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> UpdateFeedbackAsync(SubmissionFeedbackEntity feedbackEntity);

        /// <summary>
        /// Feedback'i soft delete et
        /// </summary>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <returns>Silme başarılı mı?</returns>
        Task<bool> DeleteFeedbackAsync(string feedbackId);

        /// <summary>
        /// Submission için tüm feedback'leri getir (kronolojik sırada)
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="includeInactive">Pasif feedback'leri de dahil et mi?</param>
        /// <returns>Feedback listesi</returns>
        Task<List<SubmissionFeedbackEntity>> GetFeedbacksBySubmissionIdAsync(string submissionId, bool includeInactive = false);

        /// <summary>
        /// Controller'ın verdiği tüm feedback'leri getir
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış feedback listesi</returns>
        Task<PagedListDto<SubmissionFeedbackEntity>> GetFeedbacksByControllerAsync(string controllerId, PagedListCo<FeedbackFilterCo> co);

        #endregion

        #region Criterion Feedback Operations

        /// <summary>
        /// Kriter feedback'i oluştur
        /// </summary>
        /// <param name="criterionFeedbackEntity">Kriter feedback entity</param>
        /// <returns>Oluşturulan kriter feedback entity</returns>
        Task<CriterionFeedbackEntity> CreateCriterionFeedbackAsync(CriterionFeedbackEntity criterionFeedbackEntity);

        /// <summary>
        /// Kriter feedback'i güncelle
        /// </summary>
        /// <param name="criterionFeedbackEntity">Güncellenmiş kriter feedback entity</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> UpdateCriterionFeedbackAsync(CriterionFeedbackEntity criterionFeedbackEntity);

        /// <summary>
        /// Feedback için tüm kriter feedback'lerini getir
        /// </summary>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <returns>Kriter feedback listesi</returns>
        Task<List<CriterionFeedbackEntity>> GetCriterionFeedbacksByFeedbackIdAsync(string feedbackId);

        /// <summary>
        /// Kriter feedback'i sil
        /// </summary>
        /// <param name="criterionFeedbackId">Kriter feedback ID'si</param>
        /// <returns>Silme başarılı mı?</returns>
        Task<bool> DeleteCriterionFeedbackAsync(string criterionFeedbackId);

        /// <summary>
        /// Belirli kriter için feedback'leri getir
        /// </summary>
        /// <param name="criterionLinkId">Kriter link ID'si</param>
        /// <param name="dateRange">Tarih aralığı (opsiyonel)</param>
        /// <returns>Kriter feedback listesi</returns>
        Task<List<CriterionFeedbackEntity>> GetFeedbacksByCriterionAsync(string criterionLinkId, DateRange? dateRange = null);

        #endregion

        #region Query Operations

        /// <summary>
        /// Feedback türüne göre feedback'leri getir
        /// </summary>
        /// <param name="feedbackType">Feedback türü</param>
        /// <param name="co">Pagination parametreleri</param>
        /// <returns>Sayfalanmış feedback listesi</returns>
        Task<PagedListDto<SubmissionFeedbackEntity>> GetFeedbacksByTypeAsync(string feedbackType, PagedListCo<FeedbackFilterCo> co);

        /// <summary>
        /// Akademisyen için tüm feedback'leri getir
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış feedback listesi</returns>
        Task<PagedListDto<SubmissionFeedbackEntity>> GetFeedbacksByAcademicianAsync(string academicianUserId, PagedListCo<FeedbackFilterCo> co);

        /// <summary>
        /// Aktif revision request'leri getir
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si (opsiyonel)</param>
        /// <returns>Aktif revision request listesi</returns>
        Task<List<SubmissionFeedbackEntity>> GetActiveRevisionRequestsAsync(string? academicianUserId = null);

        /// <summary>
        /// Deadline yaklaşan revision request'leri getir
        /// </summary>
        /// <param name="daysBeforeDeadline">Deadline'dan kaç gün önce</param>
        /// <returns>Deadline yaklaşan revision request listesi</returns>
        Task<List<SubmissionFeedbackEntity>> GetUpcomingDeadlineRevisionRequestsAsync(int daysBeforeDeadline);

        /// <summary>
        /// Belirli tarih aralığındaki feedback'leri getir
        /// </summary>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <param name="feedbackType">Feedback türü (opsiyonel)</param>
        /// <returns>Feedback listesi</returns>
        Task<List<SubmissionFeedbackEntity>> GetFeedbacksByDateRangeAsync(DateTime startDate, DateTime endDate, string? feedbackType = null);

        #endregion

        #region Statistics and Analytics

        /// <summary>
        /// Feedback istatistiklerini getir
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si (opsiyonel)</param>
        /// <param name="userRole">Kullanıcı rolü (Academician/Controller)</param>
        /// <param name="dateRange">Tarih aralığı (opsiyonel)</param>
        /// <returns>Feedback istatistikleri</returns>
        Task<Dictionary<string, object>> GetFeedbackStatisticsAsync(string? userId = null, string? userRole = null, DateRange? dateRange = null);

        /// <summary>
        /// Ortalama feedback süresini hesapla
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si (opsiyonel)</param>
        /// <param name="dateRange">Tarih aralığı (opsiyonel)</param>
        /// <returns>Ortalama feedback süresi (saat)</returns>
        Task<double> CalculateAverageFeedbackTimeAsync(string? controllerId = null, DateRange? dateRange = null);

        /// <summary>
        /// En çok revision talep edilen kriterleri getir
        /// </summary>
        /// <param name="limit">Sonuç limiti</param>
        /// <param name="dateRange">Tarih aralığı (opsiyonel)</param>
        /// <returns>Kriter revision istatistikleri</returns>
        Task<List<Dictionary<string, object>>> GetMostRevisedCriteriaAsync(int limit = 10, DateRange? dateRange = null);

        /// <summary>
        /// Controller performans istatistiklerini getir
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="dateRange">Tarih aralığı (opsiyonel)</param>
        /// <returns>Controller performans verileri</returns>
        Task<Dictionary<string, object>> GetControllerPerformanceStatsAsync(string controllerId, DateRange? dateRange = null);

        #endregion

        #region Notification Operations

        /// <summary>
        /// Feedback notification log'u oluştur
        /// </summary>
        /// <param name="notificationEntity">Notification log entity</param>
        /// <returns>Oluşturulan notification log entity</returns>
        Task<FeedbackNotificationLogEntity> CreateNotificationLogAsync(FeedbackNotificationLogEntity notificationEntity);

        /// <summary>
        /// Notification durumunu güncelle
        /// </summary>
        /// <param name="notificationId">Notification ID'si</param>
        /// <param name="status">Yeni durum</param>
        /// <param name="errorMessage">Hata mesajı (varsa)</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> UpdateNotificationStatusAsync(string notificationId, string status, string? errorMessage = null);

        /// <summary>
        /// Feedback için notification gönderildi mi kontrol et
        /// </summary>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <returns>Notification gönderildi mi?</returns>
        Task<bool> IsNotificationSentAsync(string feedbackId);

        /// <summary>
        /// Başarısız notification'ları getir (retry için)
        /// </summary>
        /// <param name="maxRetryCount">Maksimum retry sayısı</param>
        /// <returns>Başarısız notification listesi</returns>
        Task<List<FeedbackNotificationLogEntity>> GetFailedNotificationsAsync(int maxRetryCount = 3);

        #endregion

        #region Validation and Helper Methods

        /// <summary>
        /// Feedback var mı kontrol et
        /// </summary>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <returns>Feedback var mı?</returns>
        Task<bool> FeedbackExistsAsync(string feedbackId);

        /// <summary>
        /// Kullanıcının feedback'e erişim yetkisi var mı kontrol et
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <returns>Erişim yetkisi var mı?</returns>
        Task<bool> HasAccessToFeedbackAsync(string userId, string feedbackId);

        /// <summary>
        /// Submission için belirli türde feedback var mı kontrol et
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="feedbackType">Feedback türü</param>
        /// <returns>Feedback var mı?</returns>
        Task<bool> HasFeedbackOfTypeAsync(string submissionId, string feedbackType);

        /// <summary>
        /// Feedback'in düzenlenebilir olup olmadığını kontrol et
        /// </summary>
        /// <param name="feedbackId">Feedback ID'si</param>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <returns>Düzenlenebilir mi?</returns>
        Task<bool> CanEditFeedbackAsync(string feedbackId, string userId);

        /// <summary>
        /// Son feedback tarihini getir
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <returns>Son feedback tarihi</returns>
        Task<DateTime?> GetLastFeedbackDateAsync(string submissionId);

        #endregion

        #region Bulk Operations

        /// <summary>
        /// Submission feedback bulk create - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="feedbackEntities">Feedback entity listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> CreateBulkFeedbackAsync(List<SubmissionFeedbackEntity> feedbackEntities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Criterion feedback bulk create - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="criterionFeedbackEntities">Kriter feedback entity listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> CreateBulkCriterionFeedbackAsync(List<CriterionFeedbackEntity> criterionFeedbackEntities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Submission feedback bulk update - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="entities">Entity listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> BulkUpdateAsync(List<SubmissionFeedbackEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Submission feedback bulk delete - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="entities">Entity listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> BulkDeleteAsync(List<SubmissionFeedbackEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Eski feedback'leri arşivle
        /// </summary>
        /// <param name="olderThanDays">Kaç günden eski</param>
        /// <returns>Arşivlenen feedback sayısı</returns>
        Task<int> ArchiveOldFeedbacksAsync(int olderThanDays);

        /// <summary>
        /// Pending revision requests listesi (controller için)
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="co">Pagination ve filter criteria</param>
        /// <returns>Pending revision requests</returns>
        Task<PagedListDto<FeedbackEntryDto>> GetPendingRevisionRequestsByControllerAsync(string controllerId, PagedListCo<GetFeedbackFilterCo> co);

        /// <summary>
        /// Deadline yaklaşan revision requests
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="daysThreshold">Kaç gün kala uyarı verilecek</param>
        /// <returns>Deadline yaklaşan revision requests</returns>
        Task<List<FeedbackEntryDto>> GetUpcomingRevisionDeadlinesAsync(string controllerId, int daysThreshold);

        #endregion
    }

    // FeedbackFilterCo ve DateRange sınıfları IFeedbackManager.cs'de tanımlı
}
