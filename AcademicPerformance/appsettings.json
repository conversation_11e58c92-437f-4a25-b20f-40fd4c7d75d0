{"Kestrel": {"Certificates": {"Default": {"Path": "Certs/localhost.pfx", "Password": "1smYfuQbEHBTEB"}}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"OrganizationManagement": "Host=localhost;Database=organizationmanagement;Username=********;Password=********;Pooling=true;MinPoolSize=5;MaxPoolSize=100;ConnectionLifetime=300;CommandTimeout=60", "RlxIdentityShared": "Host=**************;Database=RlxIdentity;Username=readonly_user;Password=**************;Port=6007;Pooling=true;MinPoolSize=2;MaxPoolSize=20;ConnectionLifetime=300;CommandTimeout=30;Timeout=30", "AcademicPerformance": "Host=localhost;Database=academicperformance;Username=********;Password=********;Pooling=true;MinPoolSize=10;MaxPoolSize=200;ConnectionLifetime=300;CommandTimeout=120;Timeout=30;ApplicationName=AcademicPerformance", "Redis": "localhost:6379"}, "ApdysMongoDb": {"ConnectionString": "**************************************************************************", "DatabaseName": "ApdysDynamicData"}, "RedisCache": {"ConnectionString": "localhost:6379", "DefaultExpiration": "01:00:00", "SlidingExpiration": "00:30:00", "AbsoluteExpiration": "24:00:00"}, "DatabasePerformance": {"EnableQueryLogging": false, "EnableSensitiveDataLogging": false, "QueryTimeout": 120, "BatchSize": 1000, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:30", "EnableServiceProviderCaching": true, "EnableQuerySplitting": true, "TrackingBehavior": "NoTracking"}, "Pagination": {"DefaultPageSize": 20, "MaxPageSize": 100, "EnableTotalCountOptimization": true, "CachePagedResults": true, "CacheExpiration": "00:15:00"}, "MinIO": [{"Name": "ApdysMinio", "Endpoint": "localhost:9000", "AccessKey": "apdys-admin", "SecretKey": "apdys-secure-password-2024", "UseSSL": false, "DefaultBucket": "apdys-evidence-files", "MaxFileSize": 10485760, "AllowedExtensions": [".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png"], "AllowedMimeTypes": ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "image/jpeg", "image/png"]}], "FileUpload": {"MaxFileSize": 52428800, "MaxFilesPerBulkUpload": 10, "AllowedExtensions": [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".rtf", ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".zip", ".rar", ".7z"], "AllowedMimeTypes": ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation", "text/plain", "application/rtf", "image/jpeg", "image/png", "image/gif", "image/bmp", "application/zip", "application/x-rar-compressed", "application/x-7z-compressed"], "EnableVirusScanning": false, "EnableMagicNumberValidation": true, "MaxConcurrentUploads": 4, "UploadTimeoutSeconds": 300, "EnableProgressTracking": true, "TempFileRetentionHours": 24, "RateLimit": {"UploadsPerMinute": 10, "UploadsPerHour": 100, "MaxUploadSizePerHour": 524288000}}, "OpenIddict": {"Issuer": "https://dev-rlxidentity-api.arel.edu.tr:6010"}, "CorsOrigins": ["http://localhost:3000", "https://apdys.arel.edu.tr"], "ExternalApis": {"OrganizationManagement": {"BaseUrl": "https://dev-organizationmanagement-api.arel.edu.tr:6052/"}, "ArelBridge": {"BaseUrl": "https://localhost:7001"}}, "EntityLog": {"Host": "localhost", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "ExcludeEntities": [""]}, "RequestLog": {"Host": "localhost", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "Module": "AcademicPerformance"}, "SystemLog": {"Host": "localhost", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "Module": "AcademicPerformance"}, "EmailSettings": {"SmtpHost": "smtp.arel.edu.tr", "SmtpPort": "587", "EnableSsl": "true", "Username": "<EMAIL>", "Password": "SMTP_PASSWORD_HERE", "FromEmail": "<EMAIL>", "FromName": "APDYS - Akademik Performans Değerlendirme Sistemi", "TestMode": "false", "TestEmail": "<EMAIL>"}}