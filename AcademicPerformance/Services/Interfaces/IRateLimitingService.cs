namespace AcademicPerformance.Services.Interfaces;

/// <summary>
/// Rate limiting service for file uploads
/// </summary>
public interface IRateLimitingService
{
    /// <summary>
    /// Check if user can upload files based on rate limits
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="fileCount">Number of files to upload</param>
    /// <param name="totalSize">Total size of files in bytes</param>
    /// <returns>Rate limit check result</returns>
    Task<RateLimitResult> CheckRateLimitAsync(string userId, int fileCount = 1, long totalSize = 0);

    /// <summary>
    /// Record a file upload for rate limiting tracking
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="fileCount">Number of files uploaded</param>
    /// <param name="totalSize">Total size of files uploaded</param>
    /// <returns>Task</returns>
    Task RecordUploadAsync(string userId, int fileCount = 1, long totalSize = 0);

    /// <summary>
    /// Get current rate limit status for user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Rate limit status</returns>
    Task<RateLimitStatus> GetRateLimitStatusAsync(string userId);

    /// <summary>
    /// Reset rate limits for user (admin function)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Task</returns>
    Task ResetRateLimitsAsync(string userId);
}

/// <summary>
/// Rate limit check result
/// </summary>
public class RateLimitResult
{
    public bool IsAllowed { get; set; }
    public string? ReasonCode { get; set; }
    public string? Message { get; set; }
    public TimeSpan? RetryAfter { get; set; }
    public RateLimitStatus? CurrentStatus { get; set; }

    public static RateLimitResult Allow()
    {
        return new RateLimitResult { IsAllowed = true };
    }

    public static RateLimitResult Deny(string reasonCode, string message, TimeSpan? retryAfter = null)
    {
        return new RateLimitResult
        {
            IsAllowed = false,
            ReasonCode = reasonCode,
            Message = message,
            RetryAfter = retryAfter
        };
    }
}

/// <summary>
/// Current rate limit status for a user
/// </summary>
public class RateLimitStatus
{
    public string UserId { get; set; } = string.Empty;
    public int UploadsThisMinute { get; set; }
    public int UploadsThisHour { get; set; }
    public long UploadSizeThisHour { get; set; }
    public DateTime? LastUploadTime { get; set; }
    public DateTime? MinuteWindowStart { get; set; }
    public DateTime? HourWindowStart { get; set; }
    public int RemainingUploadsThisMinute { get; set; }
    public int RemainingUploadsThisHour { get; set; }
    public long RemainingUploadSizeThisHour { get; set; }
}
