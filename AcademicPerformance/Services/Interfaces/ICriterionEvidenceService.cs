using AcademicPerformance.Models.Configurations;
using AcademicPerformance.Models.Dtos;

namespace AcademicPerformance.Services.Interfaces;

/// <summary>
/// Service for handling criterion evidence file uploads
/// </summary>
public interface ICriterionEvidenceService
{
    /// <summary>
    /// Upload a single evidence file for a criterion
    /// </summary>
    /// <param name="request">Upload request</param>
    /// <returns>Upload result</returns>
    Task<CriterionEvidenceUploadResult> UploadSingleEvidenceAsync(UploadCriterionEvidenceRequest request);

    /// <summary>
    /// Upload multiple evidence files with criterion mappings
    /// </summary>
    /// <param name="request">Bulk upload request</param>
    /// <returns>Bulk upload result</returns>
    Task<BulkCriterionEvidenceUploadResult> UploadBulkEvidenceAsync(BulkUploadCriterionEvidenceRequest request);

    /// <summary>
    /// Validate file before upload
    /// </summary>
    /// <param name="file">File to validate</param>
    /// <returns>Validation result</returns>
    Task<FileValidationResult> ValidateFileAsync(IFormFile file);

    /// <summary>
    /// Validate multiple files before bulk upload
    /// </summary>
    /// <param name="files">Files to validate</param>
    /// <returns>Validation results</returns>
    Task<Dictionary<string, FileValidationResult>> ValidateFilesAsync(IEnumerable<IFormFile> files);

    /// <summary>
    /// Get upload progress for a specific upload operation
    /// </summary>
    /// <param name="uploadId">Upload operation ID</param>
    /// <returns>Progress information</returns>
    Task<UploadProgressInfo?> GetUploadProgressAsync(string uploadId);
}

/// <summary>
/// Request for uploading single criterion evidence
/// </summary>
public class UploadCriterionEvidenceRequest
{
    public string UserId { get; set; } = string.Empty;
    public string SubmissionId { get; set; } = string.Empty;
    public string CriterionLinkId { get; set; } = string.Empty;
    public IFormFile File { get; set; } = null!;
    public string? Description { get; set; }
    public string? UploadId { get; set; }
}

/// <summary>
/// Request for bulk uploading criterion evidence
/// </summary>
public class BulkUploadCriterionEvidenceRequest
{
    public string UserId { get; set; } = string.Empty;
    public string SubmissionId { get; set; } = string.Empty;
    public List<IFormFile> Files { get; set; } = new();
    public Dictionary<string, string> CriterionMappings { get; set; } = new(); // fileName -> criterionLinkId
    public string? Description { get; set; }
    public string? UploadId { get; set; }
}

/// <summary>
/// Result of single criterion evidence upload
/// </summary>
public class CriterionEvidenceUploadResult
{
    public bool Success { get; set; }
    public string? FileId { get; set; }
    public string? FileName { get; set; }
    public long FileSize { get; set; }
    public string? ContentType { get; set; }
    public DateTime UploadedAt { get; set; }
    public string? CriterionLinkId { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public FileValidationResult? ValidationResult { get; set; }
}

/// <summary>
/// Result of bulk criterion evidence upload
/// </summary>
public class BulkCriterionEvidenceUploadResult
{
    public bool Success { get; set; }
    public int TotalFiles { get; set; }
    public int SuccessfulUploads { get; set; }
    public int FailedUploads { get; set; }
    public List<CriterionEvidenceUploadResult> Results { get; set; } = new();
    public List<string> GeneralErrors { get; set; } = new();
    public TimeSpan TotalProcessingTime { get; set; }
    public string? UploadId { get; set; }
}

/// <summary>
/// Upload progress information
/// </summary>
public class UploadProgressInfo
{
    public string UploadId { get; set; } = string.Empty;
    public int TotalFiles { get; set; }
    public int ProcessedFiles { get; set; }
    public int SuccessfulFiles { get; set; }
    public int FailedFiles { get; set; }
    public long TotalBytes { get; set; }
    public long ProcessedBytes { get; set; }
    public double ProgressPercentage => TotalFiles > 0 ? (double)ProcessedFiles / TotalFiles * 100 : 0;
    public string Status { get; set; } = "Pending"; // Pending, Processing, Completed, Failed
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public List<string> CurrentlyProcessing { get; set; } = new();
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// File upload context for tracking
/// </summary>
public class FileUploadContext
{
    public string UploadId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string SubmissionId { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string? CriterionLinkId { get; set; }
    public DateTime StartedAt { get; set; }
    public string Status { get; set; } = "Pending";
    public string? ErrorMessage { get; set; }
}
