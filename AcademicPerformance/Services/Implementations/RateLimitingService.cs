using AcademicPerformance.Models.Configurations;
using AcademicPerformance.Services.Interfaces;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace AcademicPerformance.Services.Implementations;

/// <summary>
/// In-memory rate limiting service for file uploads
/// </summary>
public class RateLimitingService : IRateLimitingService
{
    private readonly FileUploadOptions _options;
    private readonly ILogger<RateLimitingService> _logger;
    
    // In-memory storage for rate limiting data
    private readonly ConcurrentDictionary<string, UserRateLimitData> _userLimits = new();
    private readonly object _cleanupLock = new object();
    private DateTime _lastCleanup = DateTime.UtcNow;

    public RateLimitingService(IOptions<FileUploadOptions> options, ILogger<RateLimitingService> logger)
    {
        _options = options.Value;
        _logger = logger;
    }

    public async Task<RateLimitResult> CheckRateLimitAsync(string userId, int fileCount = 1, long totalSize = 0)
    {
        await Task.CompletedTask; // Make async for future enhancements
        
        try
        {
            CleanupExpiredEntries();
            
            var userData = _userLimits.GetOrAdd(userId, _ => new UserRateLimitData { UserId = userId });
            var now = DateTime.UtcNow;

            lock (userData.Lock)
            {
                // Update windows if needed
                UpdateTimeWindows(userData, now);

                // Check minute limit
                if (userData.UploadsThisMinute + fileCount > _options.RateLimit.UploadsPerMinute)
                {
                    var retryAfter = userData.MinuteWindowStart.AddMinutes(1) - now;
                    return RateLimitResult.Deny(
                        "MINUTE_LIMIT_EXCEEDED",
                        $"Upload rate limit exceeded. Maximum {_options.RateLimit.UploadsPerMinute} uploads per minute allowed.",
                        retryAfter);
                }

                // Check hour limit
                if (userData.UploadsThisHour + fileCount > _options.RateLimit.UploadsPerHour)
                {
                    var retryAfter = userData.HourWindowStart.AddHours(1) - now;
                    return RateLimitResult.Deny(
                        "HOUR_LIMIT_EXCEEDED",
                        $"Upload rate limit exceeded. Maximum {_options.RateLimit.UploadsPerHour} uploads per hour allowed.",
                        retryAfter);
                }

                // Check size limit
                if (userData.UploadSizeThisHour + totalSize > _options.RateLimit.MaxUploadSizePerHour)
                {
                    var retryAfter = userData.HourWindowStart.AddHours(1) - now;
                    var maxSizeMB = _options.RateLimit.MaxUploadSizePerHour / (1024 * 1024);
                    return RateLimitResult.Deny(
                        "SIZE_LIMIT_EXCEEDED",
                        $"Upload size limit exceeded. Maximum {maxSizeMB}MB per hour allowed.",
                        retryAfter);
                }

                return RateLimitResult.Allow();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking rate limit for user: {UserId}", userId);
            // In case of error, allow the upload (fail open)
            return RateLimitResult.Allow();
        }
    }

    public async Task RecordUploadAsync(string userId, int fileCount = 1, long totalSize = 0)
    {
        await Task.CompletedTask; // Make async for future enhancements
        
        try
        {
            var userData = _userLimits.GetOrAdd(userId, _ => new UserRateLimitData { UserId = userId });
            var now = DateTime.UtcNow;

            lock (userData.Lock)
            {
                UpdateTimeWindows(userData, now);
                
                userData.UploadsThisMinute += fileCount;
                userData.UploadsThisHour += fileCount;
                userData.UploadSizeThisHour += totalSize;
                userData.LastUploadTime = now;
            }

            _logger.LogDebug("Recorded upload for user {UserId}: {FileCount} files, {TotalSize} bytes", 
                userId, fileCount, totalSize);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording upload for user: {UserId}", userId);
        }
    }

    public async Task<RateLimitStatus> GetRateLimitStatusAsync(string userId)
    {
        await Task.CompletedTask; // Make async for future enhancements
        
        try
        {
            CleanupExpiredEntries();
            
            if (!_userLimits.TryGetValue(userId, out var userData))
            {
                return new RateLimitStatus
                {
                    UserId = userId,
                    RemainingUploadsThisMinute = _options.RateLimit.UploadsPerMinute,
                    RemainingUploadsThisHour = _options.RateLimit.UploadsPerHour,
                    RemainingUploadSizeThisHour = _options.RateLimit.MaxUploadSizePerHour
                };
            }

            var now = DateTime.UtcNow;
            
            lock (userData.Lock)
            {
                UpdateTimeWindows(userData, now);
                
                return new RateLimitStatus
                {
                    UserId = userId,
                    UploadsThisMinute = userData.UploadsThisMinute,
                    UploadsThisHour = userData.UploadsThisHour,
                    UploadSizeThisHour = userData.UploadSizeThisHour,
                    LastUploadTime = userData.LastUploadTime,
                    MinuteWindowStart = userData.MinuteWindowStart,
                    HourWindowStart = userData.HourWindowStart,
                    RemainingUploadsThisMinute = Math.Max(0, _options.RateLimit.UploadsPerMinute - userData.UploadsThisMinute),
                    RemainingUploadsThisHour = Math.Max(0, _options.RateLimit.UploadsPerHour - userData.UploadsThisHour),
                    RemainingUploadSizeThisHour = Math.Max(0, _options.RateLimit.MaxUploadSizePerHour - userData.UploadSizeThisHour)
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rate limit status for user: {UserId}", userId);
            return new RateLimitStatus { UserId = userId };
        }
    }

    public async Task ResetRateLimitsAsync(string userId)
    {
        await Task.CompletedTask; // Make async for future enhancements
        
        try
        {
            if (_userLimits.TryRemove(userId, out var userData))
            {
                _logger.LogInformation("Reset rate limits for user: {UserId}", userId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting rate limits for user: {UserId}", userId);
        }
    }

    private void UpdateTimeWindows(UserRateLimitData userData, DateTime now)
    {
        // Initialize windows if not set
        if (userData.MinuteWindowStart == DateTime.MinValue)
        {
            userData.MinuteWindowStart = now;
            userData.HourWindowStart = now;
        }

        // Reset minute window if needed
        if (now >= userData.MinuteWindowStart.AddMinutes(1))
        {
            userData.MinuteWindowStart = now;
            userData.UploadsThisMinute = 0;
        }

        // Reset hour window if needed
        if (now >= userData.HourWindowStart.AddHours(1))
        {
            userData.HourWindowStart = now;
            userData.UploadsThisHour = 0;
            userData.UploadSizeThisHour = 0;
        }
    }

    private void CleanupExpiredEntries()
    {
        var now = DateTime.UtcNow;
        
        // Only cleanup every 5 minutes to avoid performance impact
        if (now - _lastCleanup < TimeSpan.FromMinutes(5))
            return;

        lock (_cleanupLock)
        {
            if (now - _lastCleanup < TimeSpan.FromMinutes(5))
                return;

            var expiredUsers = new List<string>();
            
            foreach (var kvp in _userLimits)
            {
                var userData = kvp.Value;
                
                // Remove entries that haven't been used for more than 2 hours
                if (userData.LastUploadTime.HasValue && 
                    now - userData.LastUploadTime.Value > TimeSpan.FromHours(2))
                {
                    expiredUsers.Add(kvp.Key);
                }
            }

            foreach (var userId in expiredUsers)
            {
                _userLimits.TryRemove(userId, out _);
            }

            _lastCleanup = now;
            
            if (expiredUsers.Count > 0)
            {
                _logger.LogDebug("Cleaned up {Count} expired rate limit entries", expiredUsers.Count);
            }
        }
    }

    private class UserRateLimitData
    {
        public string UserId { get; set; } = string.Empty;
        public int UploadsThisMinute { get; set; }
        public int UploadsThisHour { get; set; }
        public long UploadSizeThisHour { get; set; }
        public DateTime? LastUploadTime { get; set; }
        public DateTime MinuteWindowStart { get; set; } = DateTime.MinValue;
        public DateTime HourWindowStart { get; set; } = DateTime.MinValue;
        public readonly object Lock = new object();
    }
}
