using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.DbContexts;
using Microsoft.Extensions.Options;
using AcademicPerformance.Consts;
using AcademicPerformance.Controllers.Base;
using Rlx.Shared.Resources;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models;
using Rlx.Shared.Factories;
using System.Security.Cryptography;

namespace AcademicPerformance.Controllers;

/// <summary>
/// File upload controller for evidence files
/// </summary>
[ApiController]
[Route("[controller]/[action]")]
public class FileUploadController : BaseApiController
{
    private readonly MinIOFileServiceFactory _minioFileServiceFactory;
    private readonly IMinIOFileService _minioFileService;
    private readonly AcademicPerformanceDbContext _dbContext;
    private readonly MinIOConfiguration _minioConfig;
    private readonly ILogger<FileUploadController> _logger;
    private readonly IRlxSystemLogHelper<FileUploadController> _systemLogHelper;

    public FileUploadController(

        MinIOFileServiceFactory minioFileServiceFactory,

        AcademicPerformanceDbContext dbContext,
        IOptions<MinIOConfiguration> minioConfig,
        ILogger<FileUploadController> logger,
        IRlxSystemLogHelper<FileUploadController> systemLogHelper,
        IStringLocalizer<SharedResource> localizer) : base(localizer)
    {
        _minioFileServiceFactory = minioFileServiceFactory;
        _minioFileService = _minioFileServiceFactory.GetService("ApdysMinio");
        _dbContext = dbContext;
        _minioConfig = minioConfig.Value;
        _logger = logger;
        _systemLogHelper = systemLogHelper;
    }

    /// <summary>
    /// Upload a single evidence file
    /// </summary>
    /// <param name="file">File to upload</param>
    /// <param name="academicSubmissionId">Academic submission ID</param>
    /// <param name="formCriterionLinkId">Form criterion link ID (optional)</param>
    /// <param name="submittedDynamicDataInstanceId">Dynamic data instance ID (optional)</param>
    /// <param name="description">File description (optional)</param>
    /// <returns>Upload result</returns>
    [HttpPost]
    [Authorize(APConsts.Policies.UploadFiles)]
    public async Task<IActionResult> UploadEvidenceFile(
        IFormFile file,
        [FromForm] int academicSubmissionId,
        [FromForm] string? formCriterionLinkId = null,
        [FromForm] string? submittedDynamicDataInstanceId = null,
        [FromForm] string? description = null)
    {
        try
        {
            _logger.LogInformation("Evidence file upload başlatılıyor: {FileName}, AcademicSubmissionId: {AcademicSubmissionId}",
                file.FileName, academicSubmissionId);

            // Basic validation
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { status = "Error", message = "Dosya seçilmedi veya dosya boş" });
            }

            // Academic submission var mı kontrol et
            var academicSubmission = await _dbContext.AcademicSubmissions
                .FirstOrDefaultAsync(s => s.AutoIncrementId == academicSubmissionId);

            if (academicSubmission == null)
            {
                return NotFound(new { status = "Error", message = "Academic submission bulunamadı" });
            }

            // File validation
            using var stream = file.OpenReadStream();
            var validationResult = await _minioFileService.ValidateFileAsync(stream, file.FileName, file.ContentType);

            if (!validationResult.IsValid)
            {
                return BadRequest(new
                {
                    status = "Error",
                    message = "Dosya validation başarısız",
                    errors = validationResult.Errors
                });
            }

            // Generate unique object name
            var objectName = _minioFileService.GenerateUniqueObjectName(file.FileName, "evidence");

            // Upload to MinIO
            stream.Position = 0; // Reset stream position
            var uploadResult = await _minioFileService.UploadFileAsync(
                _minioConfig.DefaultBucket,
                objectName,
                stream,
                file.ContentType);

            if (!uploadResult.Success)
            {
                return StatusCode(500, new
                {
                    status = "Error",
                    message = "Dosya yükleme başarısız",
                    error = uploadResult.ErrorMessage
                });
            }

            // Calculate file checksum
            stream.Position = 0;
            var checksum = await CalculateFileChecksumAsync(stream);

            // Create database record
            var evidenceFile = new EvidenceFileEntity
            {
                Id = Guid.NewGuid().ToString(), // Required field
                AcademicSubmissionAutoIncrementId = academicSubmissionId,
                FormCriterionLinkId = formCriterionLinkId,
                SubmittedDynamicDataInstanceId = submittedDynamicDataInstanceId,
                FileName = file.FileName,
                OriginalFileName = file.FileName,
                StoredFilePath = objectName, // MinIO object name
                SizeBytes = file.Length,
                ContentType = file.ContentType,
                UploadedAt = DateTime.UtcNow,
                UploadedByUniveristyUserId = "test-user", // TODO: Get from authentication
                Description = description,

                // MinIO specific fields
                MinioBucketName = _minioConfig.DefaultBucket,
                MinioObjectName = objectName,
                MinioETag = uploadResult.ETag,
                StorageType = "MinIO",
                FileChecksum = checksum
            };

            _dbContext.EvidenceFiles.Add(evidenceFile);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("Evidence file upload başarılı: {FileName}, EvidenceFileId: {EvidenceFileId}",
                file.FileName, evidenceFile.Id);

            return Ok(new
            {
                status = "Success",
                message = "Dosya başarıyla yüklendi",
                evidenceFile = new
                {
                    id = evidenceFile.Id,
                    autoIncrementId = evidenceFile.AutoIncrementId,
                    fileName = evidenceFile.FileName,
                    originalFileName = evidenceFile.OriginalFileName,
                    sizeBytes = evidenceFile.SizeBytes,
                    contentType = evidenceFile.ContentType,
                    uploadedAt = evidenceFile.UploadedAt,
                    storageType = evidenceFile.StorageType,
                    minioBucketName = evidenceFile.MinioBucketName,
                    minioObjectName = evidenceFile.MinioObjectName,
                    minioETag = evidenceFile.MinioETag,
                    fileChecksum = evidenceFile.FileChecksum
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Evidence file upload hatası: {FileName}", file?.FileName);
            return StatusCode(500, new { status = "Error", message = "Dosya yükleme sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Upload multiple evidence files
    /// </summary>
    /// <param name="files">Files to upload</param>
    /// <param name="academicSubmissionId">Academic submission ID</param>
    /// <param name="formCriterionLinkId">Form criterion link ID (optional)</param>
    /// <param name="submittedDynamicDataInstanceId">Dynamic data instance ID (optional)</param>
    /// <param name="description">Files description (optional)</param>
    /// <returns>Upload results</returns>
    [HttpPost]
    [Authorize(APConsts.Policies.UploadFiles)]
    public async Task<IActionResult> UploadMultipleEvidenceFiles(
        List<IFormFile> files,
        [FromForm] int academicSubmissionId,
        [FromForm] string? formCriterionLinkId = null,
        [FromForm] string? submittedDynamicDataInstanceId = null,
        [FromForm] string? description = null)
    {
        try
        {
            _logger.LogInformation("Multiple evidence files upload başlatılıyor: {FileCount} dosya, AcademicSubmissionId: {AcademicSubmissionId}",
                files.Count, academicSubmissionId);

            if (files == null || files.Count == 0)
            {
                return BadRequest(new { status = "Error", message = "Dosya seçilmedi" });
            }

            // Academic submission var mı kontrol et
            var academicSubmission = await _dbContext.AcademicSubmissions
                .FirstOrDefaultAsync(s => s.AutoIncrementId == academicSubmissionId);

            if (academicSubmission == null)
            {
                return NotFound(new { status = "Error", message = "Academic submission bulunamadı" });
            }

            var results = new List<object>();
            var successCount = 0;
            var errorCount = 0;

            foreach (var file in files)
            {
                try
                {
                    if (file == null || file.Length == 0)
                    {
                        results.Add(new
                        {
                            fileName = file?.FileName ?? "unknown",
                            status = "Error",
                            message = "Dosya boş"
                        });
                        errorCount++;
                        continue;
                    }

                    // File validation
                    using var stream = file.OpenReadStream();
                    var validationResult = await _minioFileService.ValidateFileAsync(stream, file.FileName, file.ContentType);

                    if (!validationResult.IsValid)
                    {
                        results.Add(new
                        {
                            fileName = file.FileName,
                            status = "Error",
                            message = "Dosya validation başarısız",
                            errors = validationResult.Errors
                        });
                        errorCount++;
                        continue;
                    }

                    // Generate unique object name
                    var objectName = _minioFileService.GenerateUniqueObjectName(file.FileName, "evidence");

                    // Upload to MinIO
                    stream.Position = 0;
                    var uploadResult = await _minioFileService.UploadFileAsync(
                        _minioConfig.DefaultBucket,
                        objectName,
                        stream,
                        file.ContentType);

                    if (!uploadResult.Success)
                    {
                        results.Add(new
                        {
                            fileName = file.FileName,
                            status = "Error",
                            message = "MinIO upload başarısız",
                            error = uploadResult.ErrorMessage
                        });
                        errorCount++;
                        continue;
                    }

                    // Calculate file checksum
                    stream.Position = 0;
                    var checksum = await CalculateFileChecksumAsync(stream);

                    // Create database record
                    var evidenceFile = new EvidenceFileEntity
                    {
                        Id = Guid.NewGuid().ToString(), // Required field
                        AcademicSubmissionAutoIncrementId = academicSubmissionId,
                        FormCriterionLinkId = formCriterionLinkId,
                        SubmittedDynamicDataInstanceId = submittedDynamicDataInstanceId,
                        FileName = file.FileName,
                        OriginalFileName = file.FileName,
                        StoredFilePath = objectName,
                        SizeBytes = file.Length,
                        ContentType = file.ContentType,
                        UploadedAt = DateTime.UtcNow,
                        UploadedByUniveristyUserId = "test-user", // TODO: Get from authentication
                        Description = description,

                        // MinIO specific fields
                        MinioBucketName = _minioConfig.DefaultBucket,
                        MinioObjectName = objectName,
                        MinioETag = uploadResult.ETag,
                        StorageType = "MinIO",
                        FileChecksum = checksum
                    };

                    _dbContext.EvidenceFiles.Add(evidenceFile);
                    await _dbContext.SaveChangesAsync();

                    results.Add(new
                    {
                        fileName = file.FileName,
                        status = "Success",
                        message = "Dosya başarıyla yüklendi",
                        evidenceFileId = evidenceFile.Id,
                        autoIncrementId = evidenceFile.AutoIncrementId,
                        sizeBytes = evidenceFile.SizeBytes,
                        minioObjectName = evidenceFile.MinioObjectName,
                        minioETag = evidenceFile.MinioETag
                    });
                    successCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Individual file upload hatası: {FileName}", file?.FileName);
                    results.Add(new
                    {
                        fileName = file?.FileName ?? "unknown",
                        status = "Error",
                        message = "Dosya yükleme hatası",
                        error = ex.Message
                    });
                    errorCount++;
                }
            }

            _logger.LogInformation("Multiple evidence files upload tamamlandı: {SuccessCount} başarılı, {ErrorCount} hatalı",
                successCount, errorCount);

            return Ok(new
            {
                status = errorCount == 0 ? "Success" : (successCount > 0 ? "PartialSuccess" : "Error"),
                message = $"{successCount} dosya başarıyla yüklendi, {errorCount} dosya başarısız",
                totalFiles = files.Count,
                successCount = successCount,
                errorCount = errorCount,
                results = results
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Multiple evidence files upload genel hatası");
            return StatusCode(500, new { status = "Error", message = "Dosya yükleme sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Download evidence file by ID
    /// </summary>
    /// <param name="evidenceFileId">Evidence file ID</param>
    /// <returns>File download</returns>
    [HttpGet]
    [Authorize(APConsts.Policies.DownloadFiles)]
    public async Task<IActionResult> DownloadEvidenceFile(string evidenceFileId)
    {
        try
        {
            _logger.LogInformation("Evidence file download başlatılıyor: {EvidenceFileId}", evidenceFileId);

            // Evidence file var mı kontrol et
            var evidenceFile = await _dbContext.EvidenceFiles
                .FirstOrDefaultAsync(f => f.Id == evidenceFileId);

            if (evidenceFile == null)
            {
                return NotFound(new { status = "Error", message = "Evidence file bulunamadı" });
            }

            // MinIO'dan dosyayı indir
            if (evidenceFile.StorageType == "MinIO" && !string.IsNullOrEmpty(evidenceFile.MinioBucketName) && !string.IsNullOrEmpty(evidenceFile.MinioObjectName))
            {
                var fileStream = await _minioFileService.DownloadFileAsync(evidenceFile.MinioBucketName, evidenceFile.MinioObjectName);

                _logger.LogInformation("Evidence file download başarılı: {EvidenceFileId}, FileName: {FileName}",
                    evidenceFileId, evidenceFile.FileName);

                return File(fileStream, evidenceFile.ContentType, evidenceFile.FileName);
            }
            else
            {
                return StatusCode(500, new { status = "Error", message = "Dosya storage bilgileri eksik veya geçersiz" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Evidence file download hatası: {EvidenceFileId}", evidenceFileId);
            return StatusCode(500, new { status = "Error", message = "Dosya indirme sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Generate presigned URL for evidence file download
    /// </summary>
    /// <param name="evidenceFileId">Evidence file ID</param>
    /// <param name="expirySeconds">URL expiry time in seconds (default: 3600)</param>
    /// <returns>Presigned URL</returns>
    [HttpGet]
    [Authorize(APConsts.Policies.DownloadFiles)]
    public async Task<IActionResult> GeneratePresignedUrl(string evidenceFileId, [FromQuery] int expirySeconds = 3600)
    {
        try
        {
            _logger.LogInformation("Presigned URL oluşturma başlatılıyor: {EvidenceFileId}, Expiry: {ExpirySeconds}s",
                evidenceFileId, expirySeconds);

            // Evidence file var mı kontrol et
            var evidenceFile = await _dbContext.EvidenceFiles
                .FirstOrDefaultAsync(f => f.Id == evidenceFileId);

            if (evidenceFile == null)
            {
                return NotFound(new { status = "Error", message = "Evidence file bulunamadı" });
            }

            // MinIO presigned URL oluştur
            if (evidenceFile.StorageType == "MinIO" && !string.IsNullOrEmpty(evidenceFile.MinioBucketName) && !string.IsNullOrEmpty(evidenceFile.MinioObjectName))
            {
                var presignedUrl = await _minioFileService.GeneratePresignedUrlAsync(
                    evidenceFile.MinioBucketName,
                    evidenceFile.MinioObjectName,
                    expirySeconds);

                // Presigned URL expiry time'ı güncelle
                evidenceFile.MinioPresignedUrlExpiry = DateTime.UtcNow.AddSeconds(expirySeconds);
                evidenceFile.AccessUrl = presignedUrl;
                await _dbContext.SaveChangesAsync();

                _logger.LogInformation("Presigned URL oluşturma başarılı: {EvidenceFileId}", evidenceFileId);

                return Ok(new
                {
                    status = "Success",
                    message = "Presigned URL oluşturuldu",
                    evidenceFileId = evidenceFileId,
                    fileName = evidenceFile.FileName,
                    presignedUrl = presignedUrl,
                    expirySeconds = expirySeconds,
                    expiresAt = evidenceFile.MinioPresignedUrlExpiry
                });
            }
            else
            {
                return StatusCode(500, new { status = "Error", message = "Dosya storage bilgileri eksik veya geçersiz" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Presigned URL oluşturma hatası: {EvidenceFileId}", evidenceFileId);
            return StatusCode(500, new { status = "Error", message = "Presigned URL oluşturma sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Get evidence file metadata
    /// </summary>
    /// <param name="evidenceFileId">Evidence file ID</param>
    /// <returns>File metadata</returns>
    [HttpGet]
    [Authorize(APConsts.Policies.DownloadFiles)]
    public async Task<IActionResult> GetEvidenceFileMetadata(string evidenceFileId)
    {
        try
        {
            _logger.LogInformation("Evidence file metadata alma başlatılıyor: {EvidenceFileId}", evidenceFileId);

            // Evidence file var mı kontrol et
            var evidenceFile = await _dbContext.EvidenceFiles
                .Include(f => f.AcademicSubmission)
                .FirstOrDefaultAsync(f => f.Id == evidenceFileId);

            if (evidenceFile == null)
            {
                return NotFound(new { status = "Error", message = "Evidence file bulunamadı" });
            }

            _logger.LogInformation("Evidence file metadata alma başarılı: {EvidenceFileId}", evidenceFileId);

            return Ok(new
            {
                status = "Success",
                message = "Evidence file metadata alındı",
                evidenceFile = new
                {
                    id = evidenceFile.Id,
                    autoIncrementId = evidenceFile.AutoIncrementId,
                    academicSubmissionId = evidenceFile.AcademicSubmissionAutoIncrementId,
                    formCriterionLinkId = evidenceFile.FormCriterionLinkId,
                    submittedDynamicDataInstanceId = evidenceFile.SubmittedDynamicDataInstanceId,
                    fileName = evidenceFile.FileName,
                    originalFileName = evidenceFile.OriginalFileName,
                    sizeBytes = evidenceFile.SizeBytes,
                    contentType = evidenceFile.ContentType,
                    uploadedAt = evidenceFile.UploadedAt,
                    uploadedByUserId = evidenceFile.UploadedByUniveristyUserId,
                    description = evidenceFile.Description,
                    storageType = evidenceFile.StorageType,
                    minioBucketName = evidenceFile.MinioBucketName,
                    minioObjectName = evidenceFile.MinioObjectName,
                    minioETag = evidenceFile.MinioETag,
                    fileChecksum = evidenceFile.FileChecksum,
                    accessUrl = evidenceFile.AccessUrl,
                    presignedUrlExpiry = evidenceFile.MinioPresignedUrlExpiry,
                    academicSubmission = evidenceFile.AcademicSubmission != null ? new
                    {
                        id = evidenceFile.AcademicSubmission.Id,
                        autoIncrementId = evidenceFile.AcademicSubmission.AutoIncrementId,
                        academicianUserId = evidenceFile.AcademicSubmission.AcademicianUniveristyUserId,
                        status = evidenceFile.AcademicSubmission.Status
                    } : null
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Evidence file metadata alma hatası: {EvidenceFileId}", evidenceFileId);
            return StatusCode(500, new { status = "Error", message = "Metadata alma sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// List evidence files for academic submission
    /// </summary>
    /// <param name="academicSubmissionId">Academic submission ID</param>
    /// <returns>Evidence files list</returns>
    [HttpGet]
    [Authorize(APConsts.Policies.DownloadFiles)]
    public async Task<IActionResult> ListEvidenceFilesBySubmission(int academicSubmissionId)
    {
        try
        {
            _logger.LogInformation("Academic submission evidence files listeleme başlatılıyor: {AcademicSubmissionId}", academicSubmissionId);

            var evidenceFiles = await _dbContext.EvidenceFiles
                .Where(f => f.AcademicSubmissionAutoIncrementId == academicSubmissionId)
                .OrderByDescending(f => f.UploadedAt)
                .Select(f => new
                {
                    id = f.Id,
                    autoIncrementId = f.AutoIncrementId,
                    fileName = f.FileName,
                    originalFileName = f.OriginalFileName,
                    sizeBytes = f.SizeBytes,
                    contentType = f.ContentType,
                    uploadedAt = f.UploadedAt,
                    uploadedByUserId = f.UploadedByUniveristyUserId,
                    description = f.Description,
                    storageType = f.StorageType,
                    fileChecksum = f.FileChecksum,
                    hasValidPresignedUrl = f.MinioPresignedUrlExpiry.HasValue && f.MinioPresignedUrlExpiry > DateTime.UtcNow
                })
                .ToListAsync();

            _logger.LogInformation("Academic submission evidence files listeleme başarılı: {AcademicSubmissionId}, Count: {Count}",
                academicSubmissionId, evidenceFiles.Count);

            return Ok(new
            {
                status = "Success",
                message = "Evidence files listelendi",
                academicSubmissionId = academicSubmissionId,
                fileCount = evidenceFiles.Count,
                evidenceFiles = evidenceFiles
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Academic submission evidence files listeleme hatası: {AcademicSubmissionId}", academicSubmissionId);
            return StatusCode(500, new { status = "Error", message = "Evidence files listeleme sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Upload temporary file for preview/validation before final submission
    /// </summary>
    /// <param name="file">File to upload temporarily</param>
    /// <param name="description">File description (optional)</param>
    /// <returns>Temporary upload result</returns>
    [HttpPost]
    [Authorize(APConsts.Policies.UploadFiles)]
    public async Task<IActionResult> UploadTemporaryFile(
        IFormFile file,
        [FromForm] string? description = null)
    {
        try
        {
            _logger.LogInformation("Temporary file upload başlatılıyor: {FileName}", file.FileName);

            // Basic validation
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { status = "Error", message = "Dosya seçilmedi veya dosya boş" });
            }

            // File validation
            using var stream = file.OpenReadStream();
            var validationResult = await _minioFileService.ValidateFileAsync(stream, file.FileName, file.ContentType);

            if (!validationResult.IsValid)
            {
                return BadRequest(new
                {
                    status = "Error",
                    message = "Dosya validation başarısız",
                    errors = validationResult.Errors
                });
            }

            // Generate unique object name with temp prefix
            var tempObjectName = _minioFileService.GenerateUniqueObjectName(file.FileName, "temp");

            // Upload to MinIO temporary bucket/folder
            stream.Position = 0;
            var uploadResult = await _minioFileService.UploadFileAsync(
                _minioConfig.DefaultBucket,
                tempObjectName,
                stream,
                file.ContentType);

            if (!uploadResult.Success)
            {
                return StatusCode(500, new
                {
                    status = "Error",
                    message = "Temporary dosya yükleme başarısız",
                    error = uploadResult.ErrorMessage
                });
            }

            // Calculate file checksum
            stream.Position = 0;
            var checksum = await CalculateFileChecksumAsync(stream);

            // Generate presigned URL for temporary access (1 hour expiry)
            var presignedUrl = await _minioFileService.GeneratePresignedUrlAsync(
                _minioConfig.DefaultBucket,
                tempObjectName,
                3600); // 1 hour

            _logger.LogInformation("Temporary file upload başarılı: {FileName}, TempObjectName: {TempObjectName}",
                file.FileName, tempObjectName);

            return Ok(new
            {
                status = "Success",
                message = "Temporary dosya başarıyla yüklendi",
                temporaryFile = new
                {
                    tempId = tempObjectName,
                    fileName = file.FileName,
                    originalFileName = file.FileName,
                    sizeBytes = file.Length,
                    contentType = file.ContentType,
                    uploadedAt = DateTime.UtcNow,
                    description = description,
                    storageType = "MinIO",
                    minioBucketName = _minioConfig.DefaultBucket,
                    minioObjectName = tempObjectName,
                    minioETag = uploadResult.ETag,
                    fileChecksum = checksum,
                    presignedUrl = presignedUrl,
                    expiresAt = DateTime.UtcNow.AddHours(1),
                    note = "Bu dosya 24 saat sonra otomatik olarak silinecektir"
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Temporary file upload hatası: {FileName}", file?.FileName);
            return StatusCode(500, new { status = "Error", message = "Temporary dosya yükleme sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Convert temporary file to permanent evidence file
    /// </summary>
    /// <param name="tempId">Temporary file ID (MinIO object name)</param>
    /// <param name="academicSubmissionId">Academic submission ID</param>
    /// <param name="formCriterionLinkId">Form criterion link ID (optional)</param>
    /// <param name="submittedDynamicDataInstanceId">Dynamic data instance ID (optional)</param>
    /// <param name="description">File description (optional)</param>
    /// <returns>Conversion result</returns>
    [HttpPost]
    [Authorize(APConsts.Policies.UploadFiles)]
    public async Task<IActionResult> ConvertTemporaryToPermanent(
        [FromForm] string tempId,
        [FromForm] int academicSubmissionId,
        [FromForm] string? formCriterionLinkId = null,
        [FromForm] string? submittedDynamicDataInstanceId = null,
        [FromForm] string? description = null)
    {
        try
        {
            _logger.LogInformation("Temporary to permanent conversion başlatılıyor: {TempId}, AcademicSubmissionId: {AcademicSubmissionId}",
                tempId, academicSubmissionId);

            // Academic submission var mı kontrol et
            var academicSubmission = await _dbContext.AcademicSubmissions
                .FirstOrDefaultAsync(s => s.AutoIncrementId == academicSubmissionId);

            if (academicSubmission == null)
            {
                return NotFound(new { status = "Error", message = "Academic submission bulunamadı" });
            }

            // Temporary file exists mi kontrol et
            var tempFileExists = await _minioFileService.FileExistsAsync(_minioConfig.DefaultBucket, tempId);
            if (!tempFileExists)
            {
                return NotFound(new { status = "Error", message = "Temporary file bulunamadı veya süresi dolmuş" });
            }

            // Get temporary file metadata
            var tempFileMetadata = await _minioFileService.GetFileMetadataAsync(_minioConfig.DefaultBucket, tempId);

            // Generate permanent object name
            var permanentObjectName = _minioFileService.GenerateUniqueObjectName(tempFileMetadata.FileName!, "evidence");

            // Copy temporary file to permanent location
            var copyResult = await _minioFileService.CopyFileAsync(
                _minioConfig.DefaultBucket, tempId,
                _minioConfig.DefaultBucket, permanentObjectName);

            if (!copyResult.Success)
            {
                return StatusCode(500, new
                {
                    status = "Error",
                    message = "Dosya kopyalama başarısız",
                    error = copyResult.ErrorMessage
                });
            }

            // Create database record
            var evidenceFile = new EvidenceFileEntity
            {
                Id = Guid.NewGuid().ToString(),
                AcademicSubmissionAutoIncrementId = academicSubmissionId,
                FormCriterionLinkId = formCriterionLinkId,
                SubmittedDynamicDataInstanceId = submittedDynamicDataInstanceId,
                FileName = tempFileMetadata.FileName,
                OriginalFileName = tempFileMetadata.FileName,
                StoredFilePath = permanentObjectName,
                SizeBytes = tempFileMetadata.Size,
                ContentType = tempFileMetadata.ContentType,
                UploadedAt = DateTime.UtcNow,
                UploadedByUniveristyUserId = "test-user", // TODO: Get from authentication
                Description = description,

                // MinIO specific fields
                MinioBucketName = _minioConfig.DefaultBucket,
                MinioObjectName = permanentObjectName,
                MinioETag = copyResult.ETag,
                StorageType = "MinIO",
                FileChecksum = tempFileMetadata.Checksum
            };

            _dbContext.EvidenceFiles.Add(evidenceFile);
            await _dbContext.SaveChangesAsync();

            // Delete temporary file
            await _minioFileService.DeleteFileAsync(_minioConfig.DefaultBucket, tempId);

            _logger.LogInformation("Temporary to permanent conversion başarılı: {TempId} -> {PermanentObjectName}, EvidenceFileId: {EvidenceFileId}",
                tempId, permanentObjectName, evidenceFile.Id);

            return Ok(new
            {
                status = "Success",
                message = "Temporary dosya başarıyla permanent dosyaya dönüştürüldü",
                evidenceFile = new
                {
                    id = evidenceFile.Id,
                    autoIncrementId = evidenceFile.AutoIncrementId,
                    fileName = evidenceFile.FileName,
                    originalFileName = evidenceFile.OriginalFileName,
                    sizeBytes = evidenceFile.SizeBytes,
                    contentType = evidenceFile.ContentType,
                    uploadedAt = evidenceFile.UploadedAt,
                    storageType = evidenceFile.StorageType,
                    minioBucketName = evidenceFile.MinioBucketName,
                    minioObjectName = evidenceFile.MinioObjectName,
                    minioETag = evidenceFile.MinioETag,
                    fileChecksum = evidenceFile.FileChecksum
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Temporary to permanent conversion hatası: {TempId}", tempId);
            return StatusCode(500, new { status = "Error", message = "Dosya dönüştürme sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Delete temporary file
    /// </summary>
    /// <param name="tempId">Temporary file ID (MinIO object name)</param>
    /// <returns>Deletion result</returns>
    [HttpDelete]
    [Authorize(APConsts.Policies.DeleteFiles)]
    public async Task<IActionResult> DeleteTemporaryFile(string tempId)
    {
        try
        {
            _logger.LogInformation("Temporary file deletion başlatılıyor: {TempId}", tempId);

            // Check if temporary file exists
            var tempFileExists = await _minioFileService.FileExistsAsync(_minioConfig.DefaultBucket, tempId);
            if (!tempFileExists)
            {
                return NotFound(new { status = "Error", message = "Temporary file bulunamadı" });
            }

            // Delete temporary file
            var deleteResult = await _minioFileService.DeleteFileAsync(_minioConfig.DefaultBucket, tempId);

            if (!deleteResult)
            {
                return StatusCode(500, new
                {
                    status = "Error",
                    message = "Temporary dosya silme başarısız"
                });
            }

            _logger.LogInformation("Temporary file deletion başarılı: {TempId}", tempId);

            return Ok(new
            {
                status = "Success",
                message = "Temporary dosya başarıyla silindi",
                tempId = tempId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Temporary file deletion hatası: {TempId}", tempId);
            return StatusCode(500, new { status = "Error", message = "Temporary dosya silme sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Delete evidence file by ID
    /// </summary>
    /// <param name="evidenceFileId">Evidence file ID</param>
    /// <returns>Deletion result</returns>
    [HttpDelete]
    [Authorize(APConsts.Policies.DeleteFiles)]
    public async Task<IActionResult> DeleteEvidenceFile(string evidenceFileId)
    {
        try
        {
            _logger.LogInformation("Evidence file deletion başlatılıyor: {EvidenceFileId}", evidenceFileId);

            // Evidence file var mı kontrol et
            var evidenceFile = await _dbContext.EvidenceFiles
                .FirstOrDefaultAsync(f => f.Id == evidenceFileId);

            if (evidenceFile == null)
            {
                return NotFound(new { status = "Error", message = "Evidence file bulunamadı" });
            }

            // MinIO'dan dosyayı sil
            if (evidenceFile.StorageType == "MinIO" && !string.IsNullOrEmpty(evidenceFile.MinioBucketName) && !string.IsNullOrEmpty(evidenceFile.MinioObjectName))
            {
                var deleteResult = await _minioFileService.DeleteFileAsync(evidenceFile.MinioBucketName, evidenceFile.MinioObjectName);

                if (!deleteResult)
                {
                    _logger.LogWarning("MinIO dosya silme başarısız ama database kaydı silinecek: {EvidenceFileId}", evidenceFileId);
                }
            }

            // Database kaydını sil
            _dbContext.EvidenceFiles.Remove(evidenceFile);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("Evidence file deletion başarılı: {EvidenceFileId}, FileName: {FileName}",
                evidenceFileId, evidenceFile.FileName);

            return Ok(new
            {
                status = "Success",
                message = "Evidence file başarıyla silindi",
                evidenceFileId = evidenceFileId,
                fileName = evidenceFile.FileName
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Evidence file deletion hatası: {EvidenceFileId}", evidenceFileId);
            return StatusCode(500, new { status = "Error", message = "Dosya silme sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Bulk delete evidence files by IDs
    /// </summary>
    /// <param name="evidenceFileIds">Evidence file IDs to delete</param>
    /// <returns>Bulk deletion result</returns>
    [HttpDelete]
    [Authorize(APConsts.Policies.DeleteFiles)]
    public async Task<IActionResult> BulkDeleteEvidenceFiles([FromBody] List<string> evidenceFileIds)
    {
        try
        {
            _logger.LogInformation("Bulk evidence file deletion başlatılıyor: {Count} dosya", evidenceFileIds.Count);

            if (evidenceFileIds == null || evidenceFileIds.Count == 0)
            {
                return BadRequest(new { status = "Error", message = "Silinecek dosya ID'leri belirtilmedi" });
            }

            var results = new List<object>();
            var successCount = 0;
            var errorCount = 0;

            foreach (var evidenceFileId in evidenceFileIds)
            {
                try
                {
                    // Evidence file var mı kontrol et
                    var evidenceFile = await _dbContext.EvidenceFiles
                        .FirstOrDefaultAsync(f => f.Id == evidenceFileId);

                    if (evidenceFile == null)
                    {
                        results.Add(new
                        {
                            evidenceFileId = evidenceFileId,
                            status = "Error",
                            message = "Evidence file bulunamadı"
                        });
                        errorCount++;
                        continue;
                    }

                    // MinIO'dan dosyayı sil
                    if (evidenceFile.StorageType == "MinIO" && !string.IsNullOrEmpty(evidenceFile.MinioBucketName) && !string.IsNullOrEmpty(evidenceFile.MinioObjectName))
                    {
                        var deleteResult = await _minioFileService.DeleteFileAsync(evidenceFile.MinioBucketName, evidenceFile.MinioObjectName);

                        if (!deleteResult)
                        {
                            _logger.LogWarning("MinIO dosya silme başarısız ama database kaydı silinecek: {EvidenceFileId}", evidenceFileId);
                        }
                    }

                    // Database kaydını sil
                    _dbContext.EvidenceFiles.Remove(evidenceFile);
                    await _dbContext.SaveChangesAsync();

                    results.Add(new
                    {
                        evidenceFileId = evidenceFileId,
                        fileName = evidenceFile.FileName,
                        status = "Success",
                        message = "Dosya başarıyla silindi"
                    });
                    successCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Individual file deletion hatası: {EvidenceFileId}", evidenceFileId);
                    results.Add(new
                    {
                        evidenceFileId = evidenceFileId,
                        status = "Error",
                        message = "Dosya silme hatası",
                        error = ex.Message
                    });
                    errorCount++;
                }
            }

            _logger.LogInformation("Bulk evidence file deletion tamamlandı: {SuccessCount} başarılı, {ErrorCount} hatalı",
                successCount, errorCount);

            return Ok(new
            {
                status = errorCount == 0 ? "Success" : (successCount > 0 ? "PartialSuccess" : "Error"),
                message = $"{successCount} dosya başarıyla silindi, {errorCount} dosya başarısız",
                totalFiles = evidenceFileIds.Count,
                successCount = successCount,
                errorCount = errorCount,
                results = results
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Bulk evidence file deletion genel hatası");
            return StatusCode(500, new { status = "Error", message = "Bulk dosya silme sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Delete all evidence files for an academic submission
    /// </summary>
    /// <param name="academicSubmissionId">Academic submission ID</param>
    /// <returns>Deletion result</returns>
    [HttpDelete]
    [Authorize(APConsts.Policies.DeleteFiles)]
    public async Task<IActionResult> DeleteAllEvidenceFilesBySubmission(int academicSubmissionId)
    {
        try
        {
            _logger.LogInformation("Academic submission evidence files deletion başlatılıyor: {AcademicSubmissionId}", academicSubmissionId);

            // Academic submission var mı kontrol et
            var academicSubmission = await _dbContext.AcademicSubmissions
                .FirstOrDefaultAsync(s => s.AutoIncrementId == academicSubmissionId);

            if (academicSubmission == null)
            {
                return NotFound(new { status = "Error", message = "Academic submission bulunamadı" });
            }

            // Bu submission'a ait tüm evidence file'ları al
            var evidenceFiles = await _dbContext.EvidenceFiles
                .Where(f => f.AcademicSubmissionAutoIncrementId == academicSubmissionId)
                .ToListAsync();

            if (evidenceFiles.Count == 0)
            {
                return Ok(new
                {
                    status = "Success",
                    message = "Silinecek evidence file bulunamadı",
                    academicSubmissionId = academicSubmissionId,
                    deletedCount = 0
                });
            }

            var successCount = 0;
            var errorCount = 0;

            foreach (var evidenceFile in evidenceFiles)
            {
                try
                {
                    // MinIO'dan dosyayı sil
                    if (evidenceFile.StorageType == "MinIO" && !string.IsNullOrEmpty(evidenceFile.MinioBucketName) && !string.IsNullOrEmpty(evidenceFile.MinioObjectName))
                    {
                        var deleteResult = await _minioFileService.DeleteFileAsync(evidenceFile.MinioBucketName, evidenceFile.MinioObjectName);

                        if (!deleteResult)
                        {
                            _logger.LogWarning("MinIO dosya silme başarısız ama database kaydı silinecek: {EvidenceFileId}", evidenceFile.Id);
                        }
                    }

                    // Database kaydını sil
                    _dbContext.EvidenceFiles.Remove(evidenceFile);
                    successCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Individual file deletion hatası: {EvidenceFileId}", evidenceFile.Id);
                    errorCount++;
                }
            }

            // Tüm değişiklikleri kaydet
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("Academic submission evidence files deletion tamamlandı: {AcademicSubmissionId}, {SuccessCount} başarılı, {ErrorCount} hatalı",
                academicSubmissionId, successCount, errorCount);

            return Ok(new
            {
                status = errorCount == 0 ? "Success" : "PartialSuccess",
                message = $"Academic submission evidence files silindi: {successCount} başarılı, {errorCount} hatalı",
                academicSubmissionId = academicSubmissionId,
                totalFiles = evidenceFiles.Count,
                successCount = successCount,
                errorCount = errorCount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Academic submission evidence files deletion genel hatası: {AcademicSubmissionId}", academicSubmissionId);
            return StatusCode(500, new { status = "Error", message = "Academic submission dosyaları silme sırasında hata oluştu", error = ex.Message });
        }
    }

    #region Permanent File Upload Endpoint


    /// <param name="file">Yüklenecek dosya</param>
    /// <param name="academicSubmissionId">Academic submission ID</param>
    /// <param name="formCriterionLinkId">Form criterion link ID (opsiyonel)</param>
    /// <param name="submittedDynamicDataInstanceId">Submitted dynamic data instance ID (opsiyonel)</param>
    /// <param name="description">Dosya açıklaması (opsiyonel)</param>
    /// <returns>Upload sonucu</returns>
    [HttpPost]
    [Authorize(APConsts.Policies.UploadFiles)]
    public async Task<IActionResult> UploadPermanentFile(
        IFormFile file,
        [FromForm] int academicSubmissionId,
        [FromForm] string? formCriterionLinkId = null,
        [FromForm] string? submittedDynamicDataInstanceId = null,
        [FromForm] string? description = null)
    {
        try
        {
            _logger.LogInformation("Direct permanent file upload başlatıldı: AcademicSubmissionId={AcademicSubmissionId}, File={FileName}",
                academicSubmissionId, file?.FileName);

            // File validation
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { status = "Error", message = "Dosya seçilmedi veya dosya boş" });
            }

            // Use existing validation logic
            using var stream = file.OpenReadStream();
            var validationResult = await _minioFileService.ValidateFileAsync(stream, file.FileName, file.ContentType);

            if (!validationResult.IsValid)
            {
                return BadRequest(new
                {
                    status = "Error",
                    message = "Dosya validation başarısız",
                    errors = validationResult.Errors
                });
            }

            // Generate permanent object name
            var permanentObjectName = _minioFileService.GenerateUniqueObjectName(file.FileName, "evidence");

            // Upload directly to permanent location
            stream.Position = 0;
            var uploadResult = await _minioFileService.UploadFileAsync(
                _minioConfig.DefaultBucket,
                permanentObjectName,
                stream,
                file.ContentType);

            if (!uploadResult.Success)
            {
                return StatusCode(500, new
                {
                    status = "Error",
                    message = "Permanent dosya yükleme başarısız",
                    error = uploadResult.ErrorMessage
                });
            }

            // Calculate file checksum
            stream.Position = 0;
            var checksum = await CalculateFileChecksumAsync(stream);

            // Create database record
            var evidenceFile = new EvidenceFileEntity
            {
                Id = Guid.NewGuid().ToString(),
                AcademicSubmissionAutoIncrementId = academicSubmissionId,
                FormCriterionLinkId = formCriterionLinkId,
                SubmittedDynamicDataInstanceId = submittedDynamicDataInstanceId,
                FileName = file.FileName,
                OriginalFileName = file.FileName,
                StoredFilePath = permanentObjectName,
                SizeBytes = file.Length,
                ContentType = file.ContentType,
                UploadedAt = DateTime.UtcNow,
                UploadedByUniveristyUserId = "test-user", // TODO: Get from authentication
                Description = description,

                // MinIO specific fields
                MinioBucketName = _minioConfig.DefaultBucket,
                MinioObjectName = permanentObjectName,
                MinioETag = uploadResult.ETag,
                StorageType = "MinIO",
                FileChecksum = checksum
            };

            _dbContext.EvidenceFiles.Add(evidenceFile);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("Direct permanent file upload başarılı: FileId={FileId}, ObjectName={ObjectName}",
                evidenceFile.Id, permanentObjectName);

            return Ok(new
            {
                status = "Success",
                message = "Dosya başarıyla yüklendi",
                evidenceFile = new
                {
                    id = evidenceFile.Id,
                    autoIncrementId = evidenceFile.AutoIncrementId,
                    fileName = evidenceFile.FileName,
                    originalFileName = evidenceFile.OriginalFileName,
                    sizeBytes = evidenceFile.SizeBytes,
                    contentType = evidenceFile.ContentType,
                    uploadedAt = evidenceFile.UploadedAt,
                    description = evidenceFile.Description,
                    storageType = evidenceFile.StorageType,
                    minioBucketName = evidenceFile.MinioBucketName,
                    minioObjectName = evidenceFile.MinioObjectName,
                    minioETag = evidenceFile.MinioETag,
                    fileChecksum = evidenceFile.FileChecksum
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Direct permanent file upload hatası: AcademicSubmissionId={AcademicSubmissionId}",
                academicSubmissionId);
            return StatusCode(500, new { status = "Error", message = "Dosya yükleme sırasında hata oluştu", error = ex.Message });
        }
    }

    #endregion

    /// <summary>
    /// Calculate SHA256 checksum for file integrity
    /// </summary>
    private async Task<string> CalculateFileChecksumAsync(Stream stream)
    {
        using var sha256 = SHA256.Create();
        var hashBytes = await sha256.ComputeHashAsync(stream);
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    /// <summary>
    /// Helper method for single permanent file upload
    /// </summary>
    private async Task<(bool Success, object? Data, string? ErrorMessage)> UploadSinglePermanentFileAsync(
        IFormFile file,
        int academicSubmissionId,
        string? formCriterionLinkId,
        string? submittedDynamicDataInstanceId,
        string? description)
    {
        try
        {
            // Use existing validation and upload logic
            using var stream = file.OpenReadStream();
            var validationResult = await _minioFileService.ValidateFileAsync(stream, file.FileName, file.ContentType);

            if (!validationResult.IsValid)
            {
                return (false, null, $"Validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            var permanentObjectName = _minioFileService.GenerateUniqueObjectName(file.FileName, "evidence");

            stream.Position = 0;
            var uploadResult = await _minioFileService.UploadFileAsync(
                _minioConfig.DefaultBucket,
                permanentObjectName,
                stream,
                file.ContentType);

            if (!uploadResult.Success)
            {
                return (false, null, uploadResult.ErrorMessage);
            }

            stream.Position = 0;
            var checksum = await CalculateFileChecksumAsync(stream);

            var evidenceFile = new EvidenceFileEntity
            {
                Id = Guid.NewGuid().ToString(),
                AcademicSubmissionAutoIncrementId = academicSubmissionId,
                FormCriterionLinkId = formCriterionLinkId,
                SubmittedDynamicDataInstanceId = submittedDynamicDataInstanceId,
                FileName = file.FileName,
                OriginalFileName = file.FileName,
                StoredFilePath = permanentObjectName,
                SizeBytes = file.Length,
                ContentType = file.ContentType,
                UploadedAt = DateTime.UtcNow,
                UploadedByUniveristyUserId = "test-user",
                Description = description,
                MinioBucketName = _minioConfig.DefaultBucket,
                MinioObjectName = permanentObjectName,
                MinioETag = uploadResult.ETag,
                StorageType = "MinIO",
                FileChecksum = checksum
            };

            _dbContext.EvidenceFiles.Add(evidenceFile);
            await _dbContext.SaveChangesAsync();

            return (true, new
            {
                id = evidenceFile.Id,
                fileName = evidenceFile.FileName,
                sizeBytes = evidenceFile.SizeBytes,
                contentType = evidenceFile.ContentType,
                uploadedAt = evidenceFile.UploadedAt,
                checksum = evidenceFile.FileChecksum
            }, null);
        }
        catch (Exception ex)
        {
            return (false, null, ex.Message);
        }
    }
}
