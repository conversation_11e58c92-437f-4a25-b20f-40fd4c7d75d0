using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Consts;
using Rlx.Shared.Resources;
using AcademicPerformance.Services.Interfaces;
using Rlx.Shared.Interfaces;
using AcademicPerformance.Controllers.Base;
namespace AcademicPerformance.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class UserController : BaseApiController
    {
        private readonly IUserDataService _userDataService;
        private readonly IUserContextHelper _userContextHelper;
        private readonly IRlxSystemLogHelper<UserController> _systemLogHelper;
        private readonly ILogger<UserController> _logger;
        public UserController(
            IUserDataService userDataService,
            IUserContextHelper userContextHelper,
            IRlxSystemLogHelper<UserController> systemLogHelper,
            ILogger<UserController> logger,
            IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
            _userDataService = userDataService;
            _userContextHelper = userContextHelper;
            _systemLogHelper = systemLogHelper;
            _logger = logger;
        }
        /// Get current authenticated user's profile information
        /// <returns>User profile information including roles, permissions, department and academic cadre</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.AccessAP)]
        public async Task<IActionResult> GetProfile()
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                if (string.IsNullOrEmpty(userId))
                    return ErrorResponse(null, _localizer["UserNotFound"].Value);
                await _systemLogHelper.LogInfoAsync($"User {userId} requested their profile information");
                var userProfile = await _userDataService.GetUserProfileAsync(userId);
                await _systemLogHelper.LogInfoAsync($"Successfully retrieved profile for user {userId}");
                return SuccessResponse(userProfile);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving user profile", ex);
                _logger.LogError(ex, "Error retrieving user profile");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Get current authenticated user's context information (internal use)
        /// <returns>User context information for internal application use</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.AccessAP)]
        public async Task<IActionResult> GetContext()
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                if (string.IsNullOrEmpty(userId))
                    return ErrorResponse(null, _localizer["UserNotFound"].Value);
                await _systemLogHelper.LogInfoAsync($"User {userId} requested their context information");
                var userContext = await _userDataService.GetUserContextAsync(userId);
                await _systemLogHelper.LogInfoAsync($"Successfully retrieved context for user {userId}");
                return SuccessResponse(userContext);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving user context", ex);
                _logger.LogError(ex, "Error retrieving user context");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Get current authenticated user's department
        /// <returns>User's department name</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.AccessAP)]
        public async Task<IActionResult> GetDepartment()
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                if (string.IsNullOrEmpty(userId))
                    return ErrorResponse(null, _localizer["UserNotFound"].Value);
                await _systemLogHelper.LogInfoAsync($"User {userId} requested their department information");
                var department = await _userDataService.GetUserDepartmentAsync(userId);
                await _systemLogHelper.LogInfoAsync($"Successfully retrieved department for user {userId}");
                return SuccessResponse(new { department });
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving user department", ex);
                _logger.LogError(ex, "Error retrieving user department");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Get current authenticated user's academic cadre
        /// <returns>User's academic cadre</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.AccessAP)]
        public async Task<IActionResult> GetAcademicCadre()
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                if (string.IsNullOrEmpty(userId))
                    return ErrorResponse(null, _localizer["UserNotFound"].Value);
                await _systemLogHelper.LogInfoAsync($"User {userId} requested their academic cadre information");
                var academicCadre = await _userDataService.GetUserAcademicCadreAsync(userId);
                await _systemLogHelper.LogInfoAsync($"Successfully retrieved academic cadre for user {userId}");
                return SuccessResponse(new { academicCadre });
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving user academic cadre", ex);
                _logger.LogError(ex, "Error retrieving user academic cadre");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }

    }
}
