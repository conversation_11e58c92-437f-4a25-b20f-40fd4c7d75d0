using System.Reflection;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Rlx.Shared.Configs;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Factories;
using Rlx.Shared.Handlers;
using Rlx.Shared.Helpers;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Managers;
using Rlx.Shared.Middlewares;
using Rlx.Shared.Services;
using Rlx.Shared.Stores;
using StackExchange.Redis;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Services.Implementations;
using AcademicPerformance.Models.Configurations;
using Rlx.Shared.Models;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Configurations;
using AcademicPerformance.Seeds;
using AcademicPerformance.DbContexts;
using System.Security.Cryptography.X509Certificates;
var builder = WebApplication.CreateBuilder(args);
MapsterConfig.RegisterMappings();
RlxLocalizationMapsterConfig.RegisterMappings();
RlxEnumMapsterConfig.RegisterMappings();
builder.Services.AddLocalization();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Academic Performance API",
        Version = "v1",
        Description = "Academic Performance Management System API"
    });
    // Bearer token authentication için Swagger konfigürasyonu
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer",
        BearerFormat = "JWT"
    });
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
    // XML dokümantasyon dosyasını dahil et (opsiyonel)
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});
builder.Services.AddHttpContextAccessor();
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
});
builder.Services.AddDbContext<RlxIdentitySharedDbContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("RlxIdentityShared"));
});
// Configure performance settings
builder.Services.Configure<DatabasePerformanceConfiguration>(
    builder.Configuration.GetSection("DatabasePerformance"));
builder.Services.Configure<PaginationConfiguration>(
    builder.Configuration.GetSection("Pagination"));
builder.Services.Configure<RedisCacheConfiguration>(
    builder.Configuration.GetSection("RedisCache"));
builder.Services.Configure<FileUploadOptions>(
    builder.Configuration.GetSection("FileUpload"));
// builder.Services.AddDbContext<AcademicPerformanceDbContext>((serviceProvider, options) =>
// {
//     var performanceConfig = serviceProvider.GetService<IConfiguration>()!
//         .GetSection("DatabasePerformance").Get<DatabasePerformanceConfiguration>() ?? new DatabasePerformanceConfiguration();
//     var connectionString = builder.Configuration.GetConnectionString("AcademicPerformance");
//     options.UseNpgsql(connectionString, npgsqlOptions =>
//     {
//         npgsqlOptions.CommandTimeout(performanceConfig.QueryTimeout);
//         npgsqlOptions.EnableRetryOnFailure(
//             maxRetryCount: performanceConfig.MaxRetryCount,
//             maxRetryDelay: performanceConfig.MaxRetryDelay,
//             errorCodesToAdd: null);
//     });
//     // // Performance optimizations
//     // if (performanceConfig.EnableQueryLogging)
//     // {
//     //     options.LogTo(Console.WriteLine, LogLevel.Information);
//     // }
//     // if (performanceConfig.EnableSensitiveDataLogging)
//     // {
//     //     options.EnableSensitiveDataLogging();
//     // }
//     // if (performanceConfig.EnableServiceProviderCaching)
//     // {
//     //     options.EnableServiceProviderCaching();
//     // }
//     // Query splitting is configured at query level when needed
//     // Set default tracking behavior
//     var trackingBehavior = performanceConfig.TrackingBehavior switch
//     {
//         "NoTracking" => QueryTrackingBehavior.NoTracking,
//         "NoTrackingWithIdentityResolution" => QueryTrackingBehavior.NoTrackingWithIdentityResolution,
//         _ => QueryTrackingBehavior.TrackAll
//     };
//     options.UseQueryTrackingBehavior(trackingBehavior);
// });
builder.Services.AddDbContext<AcademicPerformanceLocalizationDbContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("AcademicPerformance"));
});
// SSL sertifikaları sadece Development ve Production'da kullan
X509Certificate2? encryptionCert = null;
if (builder.Environment.IsDevelopment() || builder.Environment.IsProduction())
{
    if (File.Exists("Certs/encryption.pfx"))
    {
        encryptionCert = new X509Certificate2("Certs/encryption.pfx", "YxDnlZdvPimyg5");
    }
}
builder.Services.AddAuthentication(OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme);
builder.Services.AddOpenIddict()
    .AddValidation(options =>
    {
        options.SetIssuer(builder.Configuration["OpenIddict:Issuer"]!);
        options.UseSystemNetHttp();
        options.UseAspNetCore();
        // Sertifika varsa ekle
        if (encryptionCert != null)
        {
            options.AddEncryptionCertificate(encryptionCert);
        }
    });
builder.Services.AddAuthorization(options =>
{
    options.ConfigurePolicies();
    PolicyConfig.ConfigurePolicies(options);
});
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigin", b =>
    {
        b.WithOrigins(builder.Configuration.GetSection("CorsOrigins").Get<string[]>()!)
               .AllowCredentials()
               .AllowAnyHeader()
               .AllowAnyMethod();
    });
});
// Rlx.Shared RoleClaimsTransformation kullanımı (standard implementation)
builder.Services.AddTransient<IClaimsTransformation, Rlx.Shared.Handlers.RoleClaimsTransformation>();
builder.Services.AddScoped<IRlxIdentitySharedManager, RlxIdentitySharedManager>();
builder.Services.AddScoped<IRlxIdentitySharedStore, RlxIdentitySharedStore>();
builder.Services.AddScoped<IEntityChangeLogHelper, EntityChangeLogHelper>();
builder.Services.AddScoped<IRlxLocalizationManager<AcademicPerformanceLocalizationDbContext>, RlxLocalizationManager<AcademicPerformanceLocalizationDbContext>>();
builder.Services.AddScoped<IRlxLocalizationStore<AcademicPerformanceLocalizationDbContext>, RlxLocalizationStore<AcademicPerformanceLocalizationDbContext>>();
builder.Services.AddScoped<IUserContextHelper, UserContextHelper>();
builder.Services.AddScoped<AcademicPerformance.Services.IAPAuthorizationService, AcademicPerformance.Services.APAuthorizationService>();
builder.Services.AddScoped<IConnectionMultiplexer>(sp => ConnectionMultiplexer.Connect(builder.Configuration["RedisCache:ConnectionString"]!));
builder.Services.AddScoped<IRlxCacheService, Rlx.Shared.Services.RedisCacheService>();
builder.Services.AddScoped(typeof(IRlxSystemLogHelper<>), typeof(RlxSystemLogHelper<>));
builder.Services.AddSingleton(typeof(RlxQueueServiceFactory));
builder.Services.AddScoped<ExceptionHandler>();
builder.Services.AddSingleton(typeof(MongoDbServiceFactory));
builder.Services.AddSingleton(typeof(MongoCollectionServiceFactory<>));
builder.Services.AddHttpClient("OrganizationManagementApi", client =>
{
    client.BaseAddress = new Uri(builder.Configuration["ExternalApis:OrganizationManagement:BaseUrl"]!);
    client.DefaultRequestHeaders.Add("Accept", "application/json");
});
builder.Services.AddScoped<ICacheService, AcademicPerformance.Services.Implementations.RedisCacheService>();
builder.Services.AddScoped<IStaffCompetencyCacheService, StaffCompetencyCacheService>();
builder.Services.AddScoped<UserDataService>();
builder.Services.AddScoped<IUserDataService, CachedUserDataService>();
builder.Services.AddScoped<IOrganizationManagementApiService, OrganizationManagementApiService>();
builder.Services.AddScoped<IMongoDbService, AcademicPerformance.Services.Implementations.MongoDbService>();
// Store ve Manager kayıtları gevşek bağımlılık için
builder.Services.AddScoped<ICriteriaStore, AcademicPerformance.Stores.CriteriaStore>();
builder.Services.AddScoped<IFormStore, AcademicPerformance.Stores.FormStore>();
builder.Services.AddScoped<IAcademicianStore, AcademicPerformance.Stores.AcademicianStore>();
builder.Services.AddScoped<ISubmissionStore, AcademicPerformance.Stores.SubmissionStore>();
builder.Services.AddScoped<IEvidenceFileStore, AcademicPerformance.Stores.EvidenceFileStore>();
builder.Services.AddScoped<ICriteriaManager, AcademicPerformance.Managers.CriteriaManager>();
builder.Services.AddScoped<IFormManager, AcademicPerformance.Managers.FormManager>();
builder.Services.AddScoped<IAcademicianManager, AcademicPerformance.Managers.AcademicianManager>();
builder.Services.AddScoped<ISubmissionManager, AcademicPerformance.Managers.SubmissionManager>();
builder.Services.AddScoped<IControllerManager, AcademicPerformance.Managers.ControllerManager>();
builder.Services.AddScoped<ISubmissionSecurityService, AcademicPerformance.Services.SubmissionSecurityService>();
builder.Services.AddScoped<IFeedbackStore, AcademicPerformance.Stores.FeedbackStore>();
builder.Services.AddScoped<IFeedbackManager, AcademicPerformance.Managers.FeedbackManager>();
builder.Services.AddScoped<IReportingStore, AcademicPerformance.Stores.ReportingStore>();
builder.Services.AddScoped<IReportingManager, AcademicPerformance.Managers.ReportingManager>();
builder.Services.AddScoped<IDepartmentPerformanceStore, AcademicPerformance.Stores.DepartmentPerformanceStore>();
builder.Services.AddScoped<IDepartmentPerformanceManager, AcademicPerformance.Managers.DepartmentPerformanceManager>();
builder.Services.AddScoped<INotificationService, NotificationService>();
builder.Services.AddScoped<IEmailTemplateService, EmailTemplateService>();
builder.Services.AddScoped<IGenericDataEntryManager, AcademicPerformance.Managers.GenericDataEntryManager>();
builder.Services.AddScoped<IGenericDataEntryStore, AcademicPerformance.Stores.GenericDataEntryStore>();
builder.Services.AddScoped<IPortfolioControlStore, AcademicPerformance.Stores.PortfolioControlStore>();
builder.Services.AddScoped<IPortfolioControlManager, AcademicPerformance.Managers.PortfolioControlManager>();
builder.Services.AddScoped<IArelBridgeApiService, ArelBridgeApiService>();
// HttpClient for ArelBridge API
builder.Services.AddHttpClient<IArelBridgeApiService, ArelBridgeApiService>(client =>
{
    var arelBridgeBaseUrl = builder.Configuration["ExternalApis:ArelBridge:BaseUrl"];
    if (!string.IsNullOrEmpty(arelBridgeBaseUrl))
    {
        client.BaseAddress = new Uri(arelBridgeBaseUrl);
    }
    client.Timeout = TimeSpan.FromSeconds(30);
});
// MinIO File Service Registration (Rlx.Shared)
builder.Services.AddSingleton<MinIOFileServiceFactory>();
// File Upload Service Registration
builder.Services.AddScoped<ICriterionEvidenceService, CriterionEvidenceService>();
builder.Services.AddScoped<IFileContentValidationService, FileContentValidationService>();
builder.Services.AddSingleton<IRateLimitingService, RateLimitingService>();
MapsterConfig.Configure();
var app = builder.Build();
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    // ForwardedHeaders = ForwardedHeaders.XForwardedProto | ForwardedHeaders.XForwardedFor,
    // KnownNetworks =
    // {
    //     new Microsoft.AspNetCore.HttpOverrides.IPNetwork(IPAddress.Parse("***********"), 16) // Docker ağınızı buraya yazın
    // }
    ForwardedHeaders = ForwardedHeaders.All, // Tüm header'ları kabul et
    ForwardLimit = null, // Limit kaldır (birden fazla proxy varsa)
    KnownProxies = { }, // Tüm proxy'lere güven (TEHLİKELİ, production'da kullanmayın!)
    KnownNetworks = { } // Tüm ağlara güven
});
using (var scope = app.Services.CreateScope())
{
    var dbContext = scope.ServiceProvider.GetRequiredService<AcademicPerformanceDbContext>();
    dbContext.Database.Migrate();
    var localizationDbContext = scope.ServiceProvider.GetRequiredService<AcademicPerformanceLocalizationDbContext>();
    localizationDbContext.Database.Migrate();
}
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    await DataSeed.Initialize(services);
}
app.UseMiddleware<RequestLoggingMiddleware>();
app.UseLocalization();
app.UseExceptionHandler(
    errorApp =>
{
    errorApp.Run(async context =>
    {
        var exceptionHandler = app.Services.GetRequiredService<ExceptionHandler>();
        var exceptionFeature = context.Features.Get<IExceptionHandlerPathFeature>();
        if (exceptionFeature?.Error != null)
        {
            await exceptionHandler.TryHandleAsync(context, exceptionFeature.Error, context.RequestAborted);
        }
    });
});
app.Use(async (context, next) =>
{
    var logger = app.Services.GetRequiredService<ILogger<Program>>();
    logger.LogInformation($"SCHEME: {context.Request.Scheme}");
    logger.LogInformation($"IS HTTPS: {context.Request.IsHttps}");
    await next();
});
app.UseSwagger();
app.UseSwaggerUI();
app.UseCors("AllowSpecificOrigin");
app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.Run();
