using Microsoft.EntityFrameworkCore;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;

namespace AcademicPerformance.Stores;

/// <summary>
/// Evidence file data access implementation
/// </summary>
public class EvidenceFileStore : IEvidenceFileStore
{
    private readonly AcademicPerformanceDbContext _context;
    private readonly ILogger<EvidenceFileStore> _logger;

    public EvidenceFileStore(AcademicPerformanceDbContext context, ILogger<EvidenceFileStore> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<EvidenceFileEntity> CreateEvidenceFileAsync(EvidenceFileEntity evidenceFile)
    {
        try
        {
            evidenceFile.CreatedAt = DateTime.UtcNow;
            evidenceFile.UpdatedAt = DateTime.UtcNow;

            _context.EvidenceFiles.Add(evidenceFile);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created evidence file: {FileId} for submission: {SubmissionId}", 
                evidenceFile.Id, evidenceFile.AcademicSubmissionAutoIncrementId);

            return evidenceFile;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating evidence file: {FileId}", evidenceFile.Id);
            throw;
        }
    }

    public async Task<EvidenceFileEntity?> GetEvidenceFileByIdAsync(string fileId)
    {
        try
        {
            return await _context.EvidenceFiles
                .Include(ef => ef.AcademicSubmission)
                .FirstOrDefaultAsync(ef => ef.Id == fileId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting evidence file by ID: {FileId}", fileId);
            throw;
        }
    }

    public async Task<List<EvidenceFileEntity>> GetEvidenceFilesBySubmissionIdAsync(int submissionId)
    {
        try
        {
            return await _context.EvidenceFiles
                .Where(ef => ef.AcademicSubmissionAutoIncrementId == submissionId)
                .OrderByDescending(ef => ef.UploadedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting evidence files by submission ID: {SubmissionId}", submissionId);
            throw;
        }
    }

    public async Task<List<EvidenceFileEntity>> GetEvidenceFilesByCriterionLinkIdAsync(string criterionLinkId)
    {
        try
        {
            return await _context.EvidenceFiles
                .Where(ef => ef.FormCriterionLinkId == criterionLinkId)
                .OrderByDescending(ef => ef.UploadedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting evidence files by criterion link ID: {CriterionLinkId}", criterionLinkId);
            throw;
        }
    }

    public async Task<bool> UpdateEvidenceFileAsync(EvidenceFileEntity evidenceFile)
    {
        try
        {
            evidenceFile.UpdatedAt = DateTime.UtcNow;
            _context.EvidenceFiles.Update(evidenceFile);
            var result = await _context.SaveChangesAsync();

            _logger.LogInformation("Updated evidence file: {FileId}", evidenceFile.Id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating evidence file: {FileId}", evidenceFile.Id);
            throw;
        }
    }

    public async Task<bool> DeleteEvidenceFileAsync(string fileId)
    {
        try
        {
            var evidenceFile = await _context.EvidenceFiles.FindAsync(fileId);
            if (evidenceFile == null)
                return false;

            _context.EvidenceFiles.Remove(evidenceFile);
            var result = await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted evidence file: {FileId}", fileId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting evidence file: {FileId}", fileId);
            throw;
        }
    }

    public async Task<EvidenceFileEntity?> GetEvidenceFileByObjectNameAsync(string objectName)
    {
        try
        {
            return await _context.EvidenceFiles
                .FirstOrDefaultAsync(ef => ef.MinioObjectName == objectName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting evidence file by object name: {ObjectName}", objectName);
            throw;
        }
    }

    public async Task<List<EvidenceFileEntity>> CreateEvidenceFilesBatchAsync(List<EvidenceFileEntity> evidenceFiles)
    {
        try
        {
            var now = DateTime.UtcNow;
            foreach (var file in evidenceFiles)
            {
                file.CreatedAt = now;
                file.UpdatedAt = now;
            }

            _context.EvidenceFiles.AddRange(evidenceFiles);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created {Count} evidence files in batch", evidenceFiles.Count);
            return evidenceFiles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating evidence files batch: {Count} files", evidenceFiles.Count);
            throw;
        }
    }

    public async Task<List<EvidenceFileEntity>> GetEvidenceFilesByUserIdAsync(string userId)
    {
        try
        {
            return await _context.EvidenceFiles
                .Where(ef => ef.UploadedByUniveristyUserId == userId)
                .OrderByDescending(ef => ef.UploadedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting evidence files by user ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<List<EvidenceFileEntity>> GetEvidenceFilesByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            return await _context.EvidenceFiles
                .Where(ef => ef.UploadedAt >= startDate && ef.UploadedAt <= endDate)
                .OrderByDescending(ef => ef.UploadedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting evidence files by date range: {StartDate} - {EndDate}", startDate, endDate);
            throw;
        }
    }
}
