using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Extensions;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;

namespace AcademicPerformance.Stores
{
    public class AcademicianStore : IAcademicianStore
    {
        private readonly AcademicPerformanceDbContext _context;
        private readonly ILogger<AcademicianStore> _logger;
        public AcademicianStore(
            AcademicPerformanceDbContext context,
            ILogger<AcademicianStore> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region CRUD Operations

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesAsync()
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => !ap.Deleted)
                .OrderBy(ap => ap.Name)
                .ThenBy(ap => ap.Surname)
                .ToListAsync();
        }

        public async Task<AcademicianProfileEntity?> GetAcademicianProfileByIdAsync(string id)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Include(ap => ap.Submissions)
                .FirstOrDefaultAsync(ap => ap.Id == id && !ap.Deleted);
        }

        public async Task<AcademicianProfileEntity?> GetAcademicianProfileByUniversityUserIdAsync(string universityUserId)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Include(ap => ap.Submissions)
                .FirstOrDefaultAsync(ap => ap.UniversityUserId == universityUserId && !ap.Deleted);
        }

        public async Task<AcademicianProfileEntity?> GetAcademicianProfileForUpdateAsync(string id)
        {
            return await _context.AcademicianProfiles
                .FirstOrDefaultAsync(ap => ap.Id == id && !ap.Deleted);
        }

        public async Task<AcademicianProfileEntity> CreateAcademicianProfileAsync(AcademicianProfileEntity entity, bool saveChanges = true)
        {
            entity.CreatedAt = DateTime.UtcNow;
            entity.UpdatedAt = DateTime.UtcNow;
            entity.LastSyncedAt = DateTime.UtcNow;
            entity.IsActive = true;
            entity.Deleted = false;
            entity.Disabled = false;

            // FullName oluştur
            entity.FullName = $"{entity.Name} {entity.Surname}".Trim();

            _context.AcademicianProfiles.Add(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return entity;
        }

        public async Task<bool> UpdateAcademicianProfileAsync(AcademicianProfileEntity entity, bool saveChanges = true)
        {
            entity.UpdatedAt = DateTime.UtcNow;

            // FullName güncelle
            entity.FullName = $"{entity.Name} {entity.Surname}".Trim();

            _context.AcademicianProfiles.Update(entity);
            if (saveChanges)
            {
                await _context.SaveChangesAsync();
            }
            return true;
        }

        public async Task<bool> DeleteAcademicianProfileAsync(string id)
        {
            var entity = await _context.AcademicianProfiles.FirstOrDefaultAsync(ap => ap.Id == id);
            if (entity == null)
                return false;

            _context.AcademicianProfiles.Remove(entity);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> SoftDeleteAcademicianProfileAsync(string id)
        {
            var entity = await _context.AcademicianProfiles.FirstOrDefaultAsync(ap => ap.Id == id);
            if (entity == null)
                return false;

            entity.Deleted = true;
            entity.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Filtering and Search Operations

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesByDepartmentAsync(string department)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => ap.Department == department && !ap.Deleted)
                .OrderBy(ap => ap.Name)
                .ThenBy(ap => ap.Surname)
                .ToListAsync();
        }

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesByAcademicCadreAsync(string academicCadre)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => ap.AcademicCadre == academicCadre && !ap.Deleted)
                .OrderBy(ap => ap.Name)
                .ThenBy(ap => ap.Surname)
                .ToListAsync();
        }

        public async Task<List<AcademicianProfileEntity>> GetActiveAcademicianProfilesAsync()
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => ap.IsActive && !ap.Deleted && !ap.Disabled)
                .OrderBy(ap => ap.Name)
                .ThenBy(ap => ap.Surname)
                .ToListAsync();
        }

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesRequiringSyncAsync(DateTime? olderThan = null)
        {
            var cutoffDate = olderThan ?? DateTime.UtcNow.AddHours(-24);

            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => ap.LastSyncedAt < cutoffDate && ap.IsActive && !ap.Deleted)
                .OrderBy(ap => ap.LastSyncedAt)
                .ToListAsync();
        }

        public async Task<List<AcademicianProfileEntity>> SearchAcademicianProfilesAsync(string searchTerm)
        {
            var lowerSearchTerm = searchTerm.ToLower();

            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => !ap.Deleted && (
                    ap.Name.ToLower().Contains(lowerSearchTerm) ||
                    ap.Surname.ToLower().Contains(lowerSearchTerm) ||
                    (ap.FullName != null && ap.FullName.ToLower().Contains(lowerSearchTerm)) ||
                    (ap.Email != null && ap.Email.ToLower().Contains(lowerSearchTerm)) ||
                    (ap.Department != null && ap.Department.ToLower().Contains(lowerSearchTerm))
                ))
                .OrderBy(ap => ap.Name)
                .ThenBy(ap => ap.Surname)
                .ToListAsync();
        }

        #endregion

        #region Bulk Operations

        /// <summary>
        /// Academician profiles bulk create - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult<List<AcademicianProfileEntity>>> CreateAcademicianProfilesAsync(
            List<AcademicianProfileEntity> entities,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("AcademicianStore bulk create başlatılıyor - Entity Count: {Count}", entities.Count);

                // Apply business logic using extensions
                entities.ApplyAcademicianBusinessLogic();

                // Validate entities before insert
                var validationErrors = ValidateEntitiesForInsert(entities);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk create validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult<List<AcademicianProfileEntity>>
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}",
                        Data = new List<List<AcademicianProfileEntity>>()
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkInsertAsync(
                    entities,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("AcademicianStore bulk create tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);

                    return new BulkOperationResult<List<AcademicianProfileEntity>>
                    {
                        Success = true,
                        ProcessedCount = result.ProcessedCount,
                        ElapsedTime = result.ElapsedTime,
                        BatchCount = result.BatchCount,
                        Data = new List<List<AcademicianProfileEntity>> { entities }
                    };
                }
                else
                {
                    _logger.LogError("AcademicianStore bulk create başarısız - Error: {Error}", result.ErrorMessage);
                    return new BulkOperationResult<List<AcademicianProfileEntity>>
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = result.ErrorMessage,
                        Data = new List<List<AcademicianProfileEntity>>()
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AcademicianStore bulk create işlemi sırasında hata");
                return new BulkOperationResult<List<AcademicianProfileEntity>>
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero,
                    Data = new List<List<AcademicianProfileEntity>>()
                };
            }
        }

        /// <summary>
        /// Academician profiles bulk update - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> UpdateAcademicianProfilesAsync(
            List<AcademicianProfileEntity> entities,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("AcademicianStore bulk update başlatılıyor - Entity Count: {Count}", entities.Count);

                // Apply business logic for updates
                entities.ApplyAcademicianBusinessLogic();

                // Validate entities before update
                var validationErrors = ValidateEntitiesForUpdate(entities);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk update validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}"
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkUpdateAsync(
                    entities,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("AcademicianStore bulk update tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("AcademicianStore bulk update başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AcademicianStore bulk update işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesByUniversityUserIdsAsync(List<string> universityUserIds)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => universityUserIds.Contains(ap.UniversityUserId) && !ap.Deleted)
                .ToListAsync();
        }

        #endregion

        #region Sync Operations

        public async Task<bool> UpdateLastSyncedAtAsync(string universityUserId, DateTime syncedAt, string? syncNotes = null)
        {
            var entity = await _context.AcademicianProfiles
                .FirstOrDefaultAsync(ap => ap.UniversityUserId == universityUserId);

            if (entity == null)
                return false;

            entity.LastSyncedAt = syncedAt;
            entity.UpdatedAt = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(syncNotes))
            {
                entity.SyncNotes = syncNotes;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Update last synced at bulk - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> UpdateLastSyncedAtBulkAsync(
            List<string> universityUserIds,
            DateTime syncedAt,
            string? syncNotes = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("AcademicianStore bulk sync update başlatılıyor - User Count: {Count}", universityUserIds.Count);

                // Get entities to update
                var entities = await _context.AcademicianProfiles
                    .Where(ap => universityUserIds.Contains(ap.UniversityUserId))
                    .ToListAsync(cancellationToken);

                if (!entities.Any())
                {
                    _logger.LogWarning("Sync update için entity bulunamadı - UserIds: {UserIds}", string.Join(", ", universityUserIds));
                    return new BulkOperationResult
                    {
                        Success = true,
                        ProcessedCount = 0,
                        ElapsedTime = TimeSpan.Zero
                    };
                }

                // Apply sync updates
                var now = DateTime.UtcNow;
                foreach (var entity in entities)
                {
                    entity.LastSyncedAt = syncedAt;
                    entity.UpdatedAt = now;
                    // SyncStatus property removed
                    if (!string.IsNullOrEmpty(syncNotes))
                    {
                        entity.SyncNotes = syncNotes;
                    }
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkUpdateAsync(
                    entities,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("AcademicianStore bulk sync update tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("AcademicianStore bulk sync update başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AcademicianStore bulk sync update işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        #endregion

        #region Dashboard Related Operations

        public async Task<List<AcademicianProfileEntity>> GetAcademicianProfilesWithSubmissionsAsync()
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Include(ap => ap.Submissions)
                .ThenInclude(s => s.EvaluationForm)
                .Where(ap => !ap.Deleted)
                .ToListAsync();
        }

        public async Task<AcademicianProfileEntity?> GetAcademicianProfileWithSubmissionsAsync(string universityUserId)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Include(ap => ap.Submissions)
                .ThenInclude(s => s.EvaluationForm)
                .FirstOrDefaultAsync(ap => ap.UniversityUserId == universityUserId && !ap.Deleted);
        }

        #endregion

        #region Statistics and Analytics

        public async Task<int> GetTotalAcademicianCountAsync()
        {
            return await _context.AcademicianProfiles
                .CountAsync(ap => !ap.Deleted);
        }

        public async Task<int> GetActiveAcademicianCountAsync()
        {
            return await _context.AcademicianProfiles
                .CountAsync(ap => ap.IsActive && !ap.Deleted && !ap.Disabled);
        }

        public async Task<Dictionary<string, int>> GetAcademicianCountByDepartmentAsync()
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => !ap.Deleted && ap.Department != null)
                .GroupBy(ap => ap.Department)
                .ToDictionaryAsync(g => g.Key!, g => g.Count());
        }

        public async Task<Dictionary<string, int>> GetAcademicianCountByAcademicCadreAsync()
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => !ap.Deleted && ap.AcademicCadre != null)
                .GroupBy(ap => ap.AcademicCadre)
                .ToDictionaryAsync(g => g.Key!, g => g.Count());
        }

        public async Task<int> GetAcademicianCountRequiringSyncAsync(DateTime? olderThan = null)
        {
            var cutoffDate = olderThan ?? DateTime.UtcNow.AddHours(-24);

            return await _context.AcademicianProfiles
                .CountAsync(ap => ap.LastSyncedAt < cutoffDate && ap.IsActive && !ap.Deleted);
        }

        #endregion

        #region Helper Methods

        public async Task<bool> AcademicianProfileExistsAsync(string id)
        {
            return await _context.AcademicianProfiles.AnyAsync(ap => ap.Id == id && !ap.Deleted);
        }

        public async Task<bool> AcademicianProfileExistsByUniversityUserIdAsync(string universityUserId)
        {
            return await _context.AcademicianProfiles.AnyAsync(ap => ap.UniversityUserId == universityUserId && !ap.Deleted);
        }

        public async Task<IDictionary<string, int>> IdConvertForAcademicianProfile(IEnumerable<string> profileIds)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => profileIds.Contains(ap.Id))
                .ToDictionaryAsync(ap => ap.Id, ap => ap.AutoIncrementId);
        }

        public async Task<IDictionary<string, string>> UniversityUserIdToProfileIdConvertAsync(IEnumerable<string> universityUserIds)
        {
            return await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => universityUserIds.Contains(ap.UniversityUserId))
                .ToDictionaryAsync(ap => ap.UniversityUserId, ap => ap.Id);
        }

        #endregion

        #region Validation Methods

        public async Task<bool> ValidateUniversityUserIdUniqueAsync(string universityUserId, string? excludeProfileId = null)
        {
            var query = _context.AcademicianProfiles
                .Where(ap => ap.UniversityUserId == universityUserId && !ap.Deleted);

            if (!string.IsNullOrEmpty(excludeProfileId))
            {
                query = query.Where(ap => ap.Id != excludeProfileId);
            }

            return !await query.AnyAsync();
        }

        public async Task<List<string>> GetDuplicateUniversityUserIdsAsync(List<string> universityUserIds)
        {
            var existingUserIds = await _context.AcademicianProfiles
                .AsNoTracking() // Read-only query optimization
                .Where(ap => universityUserIds.Contains(ap.UniversityUserId) && !ap.Deleted)
                .Select(ap => ap.UniversityUserId)
                .ToListAsync();

            return existingUserIds;
        }

        #endregion

        #region Private Validation Methods

        /// <summary>
        /// Insert için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForInsert(List<AcademicianProfileEntity> entities)
        {
            var errors = new List<string>();

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.UniversityUserId))
                    errors.Add($"UniversityUserId boş olamaz - Entity: {entity.Id}");

                if (string.IsNullOrEmpty(entity.Name))
                    errors.Add($"Name boş olamaz - Entity: {entity.Id}");

                if (string.IsNullOrEmpty(entity.Surname))
                    errors.Add($"Surname boş olamaz - Entity: {entity.Id}");
            }

            return errors;
        }

        /// <summary>
        /// Update için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForUpdate(List<AcademicianProfileEntity> entities)
        {
            var errors = new List<string>();

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.Id))
                    errors.Add("Update için entity Id boş olamaz");

                if (string.IsNullOrEmpty(entity.UniversityUserId))
                    errors.Add($"UniversityUserId boş olamaz - Entity: {entity.Id}");
            }

            return errors;
        }

        #endregion
    }
}
