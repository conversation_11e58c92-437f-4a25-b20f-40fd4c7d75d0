FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
# Copy solution and project files
COPY ["AcademicPerformance/AcademicPerformance.csproj", "AcademicPerformance/"]
COPY ["AcademicPerformance/nuget.config", "AcademicPerformance/"]
COPY ["Rlx.Shared/Rlx.Shared.csproj", "Rlx.Shared/"]
COPY ["Rlx.Shared/nuget.config", "Rlx.Shared/"]
# Restore dependencies
RUN dotnet restore "AcademicPerformance/AcademicPerformance.csproj"
# Copy source code
COPY ["AcademicPerformance/", "AcademicPerformance/"]
COPY ["Rlx.Shared/", "Rlx.Shared/"]
# Ensure Certs directory exists and copy certificates
RUN mkdir -p /src/AcademicPerformance/Certs
WORKDIR "/src/AcademicPerformance"
RUN dotnet build "AcademicPerformance.csproj" -c Release -o /app/build
FROM build AS publish
RUN dotnet publish "AcademicPerformance.csproj" -c Release -o /app/publish /p:UseAppHost=false
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "AcademicPerformance.dll"]
EXPOSE 8080
EXPOSE 5122
