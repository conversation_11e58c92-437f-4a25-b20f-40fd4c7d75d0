using Microsoft.EntityFrameworkCore;
using AcademicPerformance.Models.Configurations;
using AcademicPerformance.Services.Interfaces;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace AcademicPerformance.Extensions;

/// <summary>
/// Bulk operation result with detailed metrics and error information
/// </summary>
public class BulkOperationResult
{
    public bool Success { get; set; }
    public int ProcessedCount { get; set; }
    public TimeSpan ElapsedTime { get; set; }
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
    public Dictionary<string, object> Metrics { get; set; } = new();
    public int BatchCount { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
}

/// <summary>
/// Generic bulk operation result with data
/// </summary>
public class BulkOperationResult<T> : BulkOperationResult
{
    public List<T> Data { get; set; } = new();
    public List<T> FailedItems { get; set; } = new();
}

/// <summary>
/// Enhanced extension methods for bulk database operations with monitoring and error handling
/// </summary>
public static class BulkOperationExtensions
{
    /// <summary>
    /// Enhanced bulk insert with full optimization, monitoring, and error handling
    /// </summary>
    public static async Task<BulkOperationResult> BulkInsertAsync<T>(this DbContext context, IEnumerable<T> entities,
        int? batchSize = null, CancellationToken cancellationToken = default) where T : class
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new BulkOperationResult
        {
            StartedAt = DateTime.UtcNow
        };

        try
        {
            // Get services from DI container
            var serviceProvider = GetServiceProvider(context);
            var logger = serviceProvider?.GetService<ILogger>();
            var config = serviceProvider?.GetService<DatabasePerformanceConfiguration>() ?? new DatabasePerformanceConfiguration();


            var effectiveBatchSize = batchSize ?? config.BatchSize;
            var entityList = entities.ToList();

            logger?.LogInformation("Starting bulk insert for {EntityType} - {Count} entities, batch size: {BatchSize}",
                typeof(T).Name, entityList.Count, effectiveBatchSize);

            if (!entityList.Any())
            {
                result.Success = true;
                result.ProcessedCount = 0;
                result.ElapsedTime = stopwatch.Elapsed;
                result.CompletedAt = DateTime.UtcNow;
                return result;
            }

            // Disable change tracking for better performance
            var originalTrackingBehavior = context.ChangeTracker.QueryTrackingBehavior;
            context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

            try
            {
                var batchCount = 0;
                var totalBatches = (int)Math.Ceiling((double)entityList.Count / effectiveBatchSize);

                for (int i = 0; i < entityList.Count; i += effectiveBatchSize)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var batch = entityList.Skip(i).Take(effectiveBatchSize).ToList();
                    context.Set<T>().AddRange(batch);

                    var batchResult = await context.SaveChangesAsync(cancellationToken);
                    context.ChangeTracker.Clear(); // Clear to free memory

                    result.ProcessedCount += batchResult;
                    batchCount++;

                    // Progress reporting (performance monitoring removed)

                    logger?.LogDebug("Processed batch {BatchNumber}/{TotalBatches} - {BatchSize} entities",
                        batchCount, totalBatches, batch.Count);
                }

                result.BatchCount = batchCount;
                result.Success = true;
                result.ElapsedTime = stopwatch.Elapsed;
                result.CompletedAt = DateTime.UtcNow;

                // Final monitoring (performance monitoring removed)

                logger?.LogInformation("Bulk insert completed for {EntityType} - {Count} entities processed in {ElapsedMs}ms",
                    typeof(T).Name, result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
            }
            finally
            {
                context.ChangeTracker.QueryTrackingBehavior = originalTrackingBehavior;
            }
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            result.ElapsedTime = stopwatch.Elapsed;
            result.CompletedAt = DateTime.UtcNow;

            var logger = GetServiceProvider(context)?.GetService<ILogger>();
            logger?.LogError(ex, "Bulk insert failed for {EntityType} after processing {Count} entities",
                typeof(T).Name, result.ProcessedCount);

            throw;
        }

        return result;
    }

    /// <summary>
    /// Enhanced bulk update with full optimization, monitoring, and error handling
    /// </summary>
    public static async Task<BulkOperationResult> BulkUpdateAsync<T>(this DbContext context, IEnumerable<T> entities,
        int? batchSize = null, CancellationToken cancellationToken = default) where T : class
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new BulkOperationResult
        {
            StartedAt = DateTime.UtcNow
        };

        try
        {
            // Get services from DI container
            var serviceProvider = GetServiceProvider(context);
            var logger = serviceProvider?.GetService<ILogger>();
            var config = serviceProvider?.GetService<DatabasePerformanceConfiguration>() ?? new DatabasePerformanceConfiguration();


            var effectiveBatchSize = batchSize ?? config.BatchSize;
            var entityList = entities.ToList();

            logger?.LogInformation("Starting bulk update for {EntityType} - {Count} entities, batch size: {BatchSize}",
                typeof(T).Name, entityList.Count, effectiveBatchSize);

            if (!entityList.Any())
            {
                result.Success = true;
                result.ProcessedCount = 0;
                result.ElapsedTime = stopwatch.Elapsed;
                result.CompletedAt = DateTime.UtcNow;
                return result;
            }

            var batchCount = 0;
            var totalBatches = (int)Math.Ceiling((double)entityList.Count / effectiveBatchSize);

            for (int i = 0; i < entityList.Count; i += effectiveBatchSize)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var batch = entityList.Skip(i).Take(effectiveBatchSize).ToList();
                context.Set<T>().UpdateRange(batch);

                var batchResult = await context.SaveChangesAsync(cancellationToken);
                context.ChangeTracker.Clear(); // Clear to free memory

                result.ProcessedCount += batchResult;
                batchCount++;

                // Progress reporting (performance monitoring removed)

                logger?.LogDebug("Processed update batch {BatchNumber}/{TotalBatches} - {BatchSize} entities",
                    batchCount, totalBatches, batch.Count);
            }

            result.BatchCount = batchCount;
            result.Success = true;
            result.ElapsedTime = stopwatch.Elapsed;
            result.CompletedAt = DateTime.UtcNow;

            // Final monitoring (performance monitoring removed)

            logger?.LogInformation("Bulk update completed for {EntityType} - {Count} entities processed in {ElapsedMs}ms",
                typeof(T).Name, result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            result.ElapsedTime = stopwatch.Elapsed;
            result.CompletedAt = DateTime.UtcNow;

            var logger = GetServiceProvider(context)?.GetService<ILogger>();
            logger?.LogError(ex, "Bulk update failed for {EntityType} after processing {Count} entities",
                typeof(T).Name, result.ProcessedCount);

            throw;
        }

        return result;
    }

    /// <summary>
    /// Enhanced bulk delete with full optimization, monitoring, and error handling
    /// </summary>
    public static async Task<BulkOperationResult> BulkDeleteAsync<T>(this DbContext context, IEnumerable<T> entities,
        int? batchSize = null, CancellationToken cancellationToken = default) where T : class
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new BulkOperationResult
        {
            StartedAt = DateTime.UtcNow
        };

        try
        {
            // Get services from DI container
            var serviceProvider = GetServiceProvider(context);
            var logger = serviceProvider?.GetService<ILogger>();
            var config = serviceProvider?.GetService<DatabasePerformanceConfiguration>() ?? new DatabasePerformanceConfiguration();


            var effectiveBatchSize = batchSize ?? config.BatchSize;
            var entityList = entities.ToList();

            logger?.LogInformation("Starting bulk delete for {EntityType} - {Count} entities, batch size: {BatchSize}",
                typeof(T).Name, entityList.Count, effectiveBatchSize);

            if (!entityList.Any())
            {
                result.Success = true;
                result.ProcessedCount = 0;
                result.ElapsedTime = stopwatch.Elapsed;
                result.CompletedAt = DateTime.UtcNow;
                return result;
            }

            var batchCount = 0;
            var totalBatches = (int)Math.Ceiling((double)entityList.Count / effectiveBatchSize);

            for (int i = 0; i < entityList.Count; i += effectiveBatchSize)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var batch = entityList.Skip(i).Take(effectiveBatchSize).ToList();
                context.Set<T>().RemoveRange(batch);

                var batchResult = await context.SaveChangesAsync(cancellationToken);
                context.ChangeTracker.Clear(); // Clear to free memory

                result.ProcessedCount += batchResult;
                batchCount++;

                // Progress reporting (performance monitoring removed)

                logger?.LogDebug("Processed delete batch {BatchNumber}/{TotalBatches} - {BatchSize} entities",
                    batchCount, totalBatches, batch.Count);
            }

            result.BatchCount = batchCount;
            result.Success = true;
            result.ElapsedTime = stopwatch.Elapsed;
            result.CompletedAt = DateTime.UtcNow;

            // Final monitoring (performance monitoring removed)

            logger?.LogInformation("Bulk delete completed for {EntityType} - {Count} entities processed in {ElapsedMs}ms",
                typeof(T).Name, result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            result.ElapsedTime = stopwatch.Elapsed;
            result.CompletedAt = DateTime.UtcNow;

            var logger = GetServiceProvider(context)?.GetService<ILogger>();
            logger?.LogError(ex, "Bulk delete failed for {EntityType} after processing {Count} entities",
                typeof(T).Name, result.ProcessedCount);

            throw;
        }

        return result;
    }

    /// <summary>
    /// Helper method to get service provider from DbContext
    /// </summary>
    private static IServiceProvider? GetServiceProvider(DbContext context)
    {
        try
        {
            // Try to get service provider from context
            var serviceProviderProperty = context.GetType().GetProperty("ServiceProvider",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (serviceProviderProperty != null)
            {
                return serviceProviderProperty.GetValue(context) as IServiceProvider;
            }

            // Alternative approach using context options
            var optionsProperty = context.GetType().GetProperty("Options",
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);

            if (optionsProperty?.GetValue(context) is DbContextOptions options)
            {
                var serviceProviderExtension = options.Extensions
                    .OfType<Microsoft.EntityFrameworkCore.Infrastructure.CoreOptionsExtension>()
                    .FirstOrDefault();

                return serviceProviderExtension?.ApplicationServiceProvider;
            }
        }
        catch
        {
            // If we can't get the service provider, return null and use defaults
        }

        return null;
    }

    /// <summary>
    /// Optimize query for large datasets
    /// </summary>
    public static IQueryable<T> OptimizeForLargeDataset<T>(this IQueryable<T> query) where T : class
    {
        return query
            .AsNoTracking() // Disable change tracking
            .AsSplitQuery(); // Use split queries for better performance with includes
    }

    /// <summary>
    /// Add pagination with performance optimization
    /// </summary>
    public static IQueryable<T> AddPagination<T>(this IQueryable<T> query, int pageNumber, int pageSize,
        int maxPageSize = 100) where T : class
    {
        // Ensure page size doesn't exceed maximum
        var effectivePageSize = Math.Min(pageSize, maxPageSize);
        var skip = Math.Max(0, (pageNumber - 1) * effectivePageSize);

        return query
            .Skip(skip)
            .Take(effectivePageSize);
    }

    /// <summary>
    /// Get optimized count for pagination
    /// </summary>
    public static async Task<int> GetOptimizedCountAsync<T>(this IQueryable<T> query,
        bool enableOptimization = true, CancellationToken cancellationToken = default)
    {
        if (!enableOptimization)
        {
            return await query.CountAsync(cancellationToken);
        }

        // For large datasets, use approximate count or limit the count
        const int maxCountThreshold = 10000;

        // First check if we have more than threshold
        var hasMoreThanThreshold = await query.Take(maxCountThreshold + 1).CountAsync(cancellationToken);

        if (hasMoreThanThreshold > maxCountThreshold)
        {
            // Return approximate count for very large datasets
            return maxCountThreshold;
        }

        return hasMoreThanThreshold;
    }
}
