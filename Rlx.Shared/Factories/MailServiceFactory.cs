using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models;
using Rlx.Shared.Services;
namespace Rlx.Shared.Factories;
public class MailServiceFactory
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<MailService> _logger;
    private readonly Dictionary<string, IOptions<MailConfig>> _mailConfigs;
    public MailServiceFactory(IConfiguration configuration, ILogger<MailService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        var sections = _configuration.GetSection("Mail").Get<List<MailConfig>>();
        if (sections == null || !sections.Any())
        {
            throw new ArgumentException("No mail configurations found in appsettings.");
        }
        _mailConfigs = new Dictionary<string, IOptions<MailConfig>>();
        foreach (var config in sections)
        {
            _mailConfigs[config.Name] = Options.Create(config);
        }
    }
    public IMailService GetService(string name)
    {
        return new MailService(_mailConfigs[name], _logger);
    }
}