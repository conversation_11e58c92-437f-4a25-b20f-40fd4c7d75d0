using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Minio;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models;
using Rlx.Shared.Services;
namespace Rlx.Shared.Factories;
public class MinIOFileServiceFactory
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<MinIOFileService> _logger;
    private readonly Dictionary<string, IMinioClient> _clients;
    private readonly Dictionary<string, IOptions<MinIOConfiguration>> _clientConfigs;
    public MinIOFileServiceFactory(IConfiguration configuration, ILogger<MinIOFileService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        var sections = configuration.GetSection("MinIO").Get<List<MinIOConfiguration>>();
        if (sections == null || !sections.Any())
        {
            throw new ArgumentException("No MinIO configurations found in appsettings.");
        }
        _clients = new Dictionary<string, IMinioClient>();
        _clientConfigs = new Dictionary<string, IOptions<MinIOConfiguration>>();
        foreach (var config in sections)
        {
            var client = new MinioClient()
                .WithEndpoint(config.Endpoint)
                .WithCredentials(config.AccessKey, config.SecretKey)
                .WithSSL(config.UseSSL)
                .Build();
            _clients[config.Name] = client;
            _clientConfigs[config.Name] = Options.Create(config);
        }
    }
    public IMinIOFileService GetService(string name)
    {
        return new MinIOFileService(_clients[name], _clientConfigs[name], _logger);
    }
}