using Rlx.Shared.DbContexts;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace Rlx.Shared.Interfaces;

public interface IRlxEnumStore<TContext> where TContext : RlxEnumDbContext
{
    Task<RlxEnumDto> GetEnumAsync(string id);
    Task<IEnumerable<RlxEnumForViewDto>> GetActiveEnums(IEnumerable<string> ids);
    Task<RlxEnum?> GetEnumForUpdateAsync(string id);
    Task<IEnumerable<RlxEnum>> GetEnumsForUpdateAsync(string[] ids);
    Task<RlxEnum> AddEnumAsync(RlxEnum rlxEnum, bool saveChanges = false);
    Task<RlxEnum> UpdateEnumAsync(RlxEnum rlxEnum, bool saveChanges = false);
    Task<IEnumerable<RlxEnum>> AddEnumsAsync(IEnumerable<RlxEnum> rlxEnums, bool saveChanges = false);
    Task<IEnumerable<RlxEnum>> UpdateEnumsAsync(IEnumerable<RlxEnum> rlxEnums, bool saveChanges = false);
    Task<RlxEnumValue?> GetEnumValueForUpdateAsync(string id);
    Task<IEnumerable<RlxEnumValue>?> GetEnumValuesForUpdateAsync(int enumId);
    Task<IEnumerable<RlxEnumValue>> GetEnumValuesForUpdateAsync(string[] ids);
    Task<RlxEnumValue> AddEnumValueAsync(RlxEnumValue rlxEnumValue, bool saveChanges = false);
    Task<IEnumerable<RlxEnumValue>> AddEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = false);
    Task<IEnumerable<RlxEnumValue>> UpdateEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = false);
    Task<IDictionary<string, int>> IdConvertForEnumValue(IEnumerable<string> enumValueIds);
}