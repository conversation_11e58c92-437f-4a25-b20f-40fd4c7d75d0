using Rlx.Shared.DbContexts;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace Rlx.Shared.Interfaces;

public interface IRlxEnumManager<TContext> where TContext : RlxEnumDbContext
{
   Task SaveEnumsAsync(IEnumerable<RlxEnumSaveDto> dtos);
   Task<IDictionary<string, int>> IdConvertForEnumValue(IEnumerable<string> enumValueIds);
   Task<IEnumerable<RlxEnumForViewDto>> GetActiveEnums(IEnumerable<string> ids);
   Task<IEnumerable<RlxEnum>> AddEnums(IEnumerable<RlxEnumAddDto> dtos, bool saveChanges = false);
   Task<IEnumerable<RlxEnum>> UpdateEnums(IEnumerable<RlxEnumUpdateDto> dtos, bool saveChanges = false);
}