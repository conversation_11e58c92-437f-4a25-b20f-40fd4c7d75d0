using Rlx.Shared.Models;
namespace Rlx.Shared.Interfaces;
public interface IMinIOFileService
{
    /// <summary>
    /// Dosya yükleme - stream ile
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı (dosya yolu)</param>
    /// <param name="stream">Dosya stream'i</param>
    /// <param name="contentType">MIME type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Upload sonucu</returns>
    Task<MinIOUploadResult> UploadFileAsync(
        string bucketName,
        string objectName,
        Stream stream,
        string contentType,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Dosya yükleme - byte array ile
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="data">Dosya verisi</param>
    /// <param name="contentType">MIME type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Upload sonucu</returns>
    Task<MinIOUploadResult> UploadFileAsync(
        string bucketName,
        string objectName,
        byte[] data,
        string contentType,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Dosya indirme - stream olarak
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dosya stream'i</returns>
    Task<Stream> DownloadFileAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Dosya indirme - byte array olarak
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dosya verisi</returns>
    Task<byte[]> DownloadFileBytesAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Dosya silme
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Silme başarılı mı</returns>
    Task<bool> DeleteFileAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Dosya var mı kontrolü
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dosya var mı</returns>
    Task<bool> FileExistsAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Dosya metadata'sını alma
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dosya metadata'sı</returns>
    Task<MinIOFileMetadata?> GetFileMetadataAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Presigned URL oluşturma (güvenli download için)
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="expirySeconds">URL geçerlilik süresi (saniye)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Presigned URL</returns>
    Task<string> GeneratePresignedUrlAsync(
        string bucketName,
        string objectName,
        int expirySeconds,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Bucket'taki dosyaları listeleme
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="prefix">Dosya prefix'i (klasör gibi)</param>
    /// <param name="recursive">Alt klasörleri de dahil et</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dosya listesi</returns>
    Task<List<MinIOFileInfo>> ListFilesAsync(
        string bucketName,
        string? prefix = null,
        bool recursive = false,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Bucket var mı kontrolü
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bucket var mı</returns>
    Task<bool> BucketExistsAsync(
        string bucketName,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Bucket oluşturma
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Oluşturma başarılı mı</returns>
    Task<bool> CreateBucketAsync(
        string bucketName,
        CancellationToken cancellationToken = default);
    /// <summary>
    /// Dosya validasyonu (boyut, tip, vs.)
    /// </summary>
    /// <param name="stream">Dosya stream'i</param>
    /// <param name="fileName">Dosya adı</param>
    /// <param name="contentType">MIME type</param>
    /// <returns>Validation sonucu</returns>
    Task<MinIOValidationResult> ValidateFileAsync(
        Stream stream,
        string fileName,
        string contentType);
    /// <summary>
    /// Unique object name oluşturma
    /// </summary>
    /// <param name="originalFileName">Orijinal dosya adı</param>
    /// <param name="prefix">Prefix (klasör yapısı için)</param>
    /// <returns>Unique object name</returns>
    string GenerateUniqueObjectName(string originalFileName, string? prefix = null);
    /// <summary>
    /// Dosya kopyalama (MinIO içinde)
    /// </summary>
    /// <param name="sourceBucket">Kaynak bucket</param>
    /// <param name="sourceObject">Kaynak object</param>
    /// <param name="destBucket">Hedef bucket</param>
    /// <param name="destObject">Hedef object</param>
    /// <returns>Kopyalama sonucu</returns>
    Task<MinIOUploadResult> CopyFileAsync(string sourceBucket, string sourceObject, string destBucket, string destObject);
}