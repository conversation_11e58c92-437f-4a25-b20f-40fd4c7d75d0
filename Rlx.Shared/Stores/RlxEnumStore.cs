using Microsoft.EntityFrameworkCore;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace Rlx.Shared.Stores;

public class RlxEnumStore<TContext> : IRlxEnumStore<TContext> where TContext : RlxEnumDbContext
{
    private readonly TContext _context;
    public RlxEnumStore(TContext context)
    {
        _context = context;
    }
    public Task<RlxEnumDto> GetEnumAsync(string id)
    {
        throw new NotImplementedException();
    }
    public async Task<IEnumerable<RlxEnumForViewDto>> GetActiveEnums(IEnumerable<string> ids)
    {
        if (ids == null || !ids.Any())
            return Enumerable.Empty<RlxEnumForViewDto>();
        var result = await _context.RlxEnums.Where(x => x.Deleted == false && x.Disabled == false && ids.Contains(x.Id))
        .Select(x => new RlxEnumForViewDto
        {
            Id = x.Id,
            Name = x.Name,
            Values = x.RlxEnumValues.Where(y => y.Deleted == false && y.Disabled == false).Select(y => new RlxEnumValueForViewDto
            {
                Id = y.Id,
                Name = y.Name,
                WhichRow = y.WhichRow,
            }).ToList()
        }).ToListAsync();
        return result;
    }
    public async Task<RlxEnum?> GetEnumForUpdateAsync(string id)
    {
        return await _context.RlxEnums.FirstOrDefaultAsync(x => x.Id == id);
    }
    public async Task<IEnumerable<RlxEnum>> GetEnumsForUpdateAsync(string[] ids)
    {
        return await _context.RlxEnums.Where(x => ids.Contains(x.Id)).ToListAsync();
    }
    public async Task<RlxEnum> AddEnumAsync(RlxEnum rlxEnum, bool saveChanges = true)
    {
        await _context.RlxEnums.AddAsync(rlxEnum);
        if (saveChanges)
            await _context.SaveChangesAsync();
        return rlxEnum;
    }
    public async Task<RlxEnum> UpdateEnumAsync(RlxEnum rlxEnum, bool saveChanges = true)
    {
        _context.RlxEnums.Update(rlxEnum);
        if (saveChanges)
            await _context.SaveChangesAsync();
        return rlxEnum;
    }
    public async Task<IEnumerable<RlxEnum>> AddEnumsAsync(IEnumerable<RlxEnum> rlxEnums, bool saveChanges = true)
    {
        await _context.RlxEnums.AddRangeAsync(rlxEnums);
        if (saveChanges)
            await _context.SaveChangesAsync();
        return rlxEnums;
    }
    public async Task<IEnumerable<RlxEnum>> UpdateEnumsAsync(IEnumerable<RlxEnum> rlxEnums, bool saveChanges = true)
    {
        _context.RlxEnums.UpdateRange(rlxEnums);
        if (saveChanges)
            await _context.SaveChangesAsync();
        return rlxEnums;
    }
    public async Task<RlxEnumValue?> GetEnumValueForUpdateAsync(string id)
    {
        return await _context.RlxEnumValues.FirstOrDefaultAsync(x => x.Id == id);
    }
    public async Task<IEnumerable<RlxEnumValue>?> GetEnumValuesForUpdateAsync(int enumId)
    {
        return await _context.RlxEnumValues.Where(x => x.EnumId == enumId).ToListAsync();
    }
    public async Task<IEnumerable<RlxEnumValue>> GetEnumValuesForUpdateAsync(string[] ids)
    {
        return await _context.RlxEnumValues.Where(x => ids.Contains(x.Id)).ToListAsync();
    }
    public async Task<RlxEnumValue> AddEnumValueAsync(RlxEnumValue rlxEnumValue, bool saveChanges = false)
    {
        await _context.RlxEnumValues.AddAsync(rlxEnumValue);
        if (saveChanges)
            await _context.SaveChangesAsync();
        return rlxEnumValue;
    }
    public async Task<IEnumerable<RlxEnumValue>> AddEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = false)
    {
        await _context.RlxEnumValues.AddRangeAsync(rlxEnumValues);
        if (saveChanges)
            await _context.SaveChangesAsync();
        return rlxEnumValues;
    }
    public async Task<RlxEnumValue> UpdateEnumValueAsync(RlxEnumValue rlxEnumValue, bool saveChanges = false)
    {
        _context.RlxEnumValues.Update(rlxEnumValue);
        if (saveChanges)
            await _context.SaveChangesAsync();
        return rlxEnumValue;
    }
    public async Task<IEnumerable<RlxEnumValue>> UpdateEnumValuesAsync(IEnumerable<RlxEnumValue> rlxEnumValues, bool saveChanges = false)
    {
        _context.RlxEnumValues.UpdateRange(rlxEnumValues);
        if (saveChanges)
            await _context.SaveChangesAsync();
        return rlxEnumValues;
    }
    public async Task<IDictionary<string, int>> IdConvertForEnumValue(IEnumerable<string> enumValueIds)
    {
        return await _context.RlxEnumValues
             .Where(x => enumValueIds.Contains(x.Id))
             .ToDictionaryAsync(x => x.Id, x => x.AutoIncrementId);
    }
}