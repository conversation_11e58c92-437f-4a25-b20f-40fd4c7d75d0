using Microsoft.Extensions.Options;
using Minio;
using Minio.DataModel.Args;
using Rlx.Shared.Models;
using Rlx.Shared.Interfaces;
using Microsoft.Extensions.Logging;
using MimeKit;
using MailKit.Net.Smtp;
using MailKit.Security;
using System.Text;
namespace Rlx.Shared.Services;
/// <summary>
/// MinIO file operations service implementation
/// </summary>
public class MailService : IMailService
{
    private readonly MailConfig _config;
    private readonly ILogger<MailService> _logger;
    public MailService(
        IOptions<MailConfig> config,
        ILogger<MailService> logger)
    {
        _config = config.Value;
        _logger = logger;
    }
    public async Task<int> SendEmailAsync(List<string> recipients, string subject, string body, bool isHtml = false)
    {
        using var client = new SmtpClient();
        int successCount = 0;
        try
        {
            await client.ConnectAsync(_config.SmtpServer, _config.SmtpPort, _config.UseSsl ? SecureSocketOptions.SslOnConnect : SecureSocketOptions.StartTls);
            await client.AuthenticateAsync(_config.Username, _config.Password);
            foreach (var recipient in recipients)
            {
                var message = new MimeMessage();
                message.From.Add(new MailboxAddress(_config.FromName, _config.FromEmail));
                message.To.Add(new MailboxAddress("", recipient));
                message.Subject = subject;
                var bodyBuilder = new BodyBuilder();
                if (isHtml)
                {
                    bodyBuilder.HtmlBody = body;
                }
                else
                {
                    bodyBuilder.TextBody = body;
                }
                message.Body = bodyBuilder.ToMessageBody();
                await client.SendAsync(message);
                successCount++;
                _logger.LogInformation($"Email sent: {recipient} - {subject}");
                // Rate limiting - 100ms delay between emails
                await Task.Delay(100);
            }
            await client.DisconnectAsync(true);
            _logger.LogInformation($"Bulk email sending completed: {successCount}/{recipients.Count} successful");
            return successCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Email sending error");
            return successCount; // Return the count of successfully sent emails
        }
    }
}
