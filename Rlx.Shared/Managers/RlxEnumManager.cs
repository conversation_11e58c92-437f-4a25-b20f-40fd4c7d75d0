using Mapster;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.RlxEnumDbContextModels;
namespace Rlx.Shared.Managers;

public class RlxEnumManager<TContext> : IRlxEnumManager<TContext> where TContext : RlxEnumDbContext
{
    private readonly IRlxEnumStore<TContext> _rlxEnumStore;
    private readonly TContext _context;
    public RlxEnumManager(IRlxEnumStore<TContext> rlxEnumStore, TContext context)
    {
        _context = context;
        _rlxEnumStore = rlxEnumStore;
    }
    public async Task<IEnumerable<RlxEnum>> AddEnums(IEnumerable<RlxEnumAddDto> dtos, bool saveChanges = false)
    {
        if (dtos == null || !dtos.Any())
            return await Task.FromResult(Enumerable.Empty<RlxEnum>());
        var addList = new List<RlxEnum>();
        foreach (var dto in dtos)
        {
            var addEnum = dto.Adapt<RlxEnum>();
            addList.Add(addEnum);
            if (dto.Values != null)
            {
                var addEnumValues = await AddEnumValues(dto.Values, false);
                addEnum.RlxEnumValues = addEnumValues.ToList();
            }
        }
        return await _rlxEnumStore.AddEnumsAsync(addList, saveChanges);
    }
    public async Task<IEnumerable<RlxEnum>> UpdateEnums(IEnumerable<RlxEnumUpdateDto> dtos, bool saveChanges = false)
    {
        if (dtos == null || !dtos.Any())
            return await Task.FromResult(Enumerable.Empty<RlxEnum>());
        var ids = dtos.Select(x => x.Id).Where(x => x != null).ToArray();
        var existingEnums = (await _rlxEnumStore.GetEnumsForUpdateAsync(ids)).ToDictionary(x => x.Id);
        var updateList = new List<RlxEnum>();
        if (existingEnums == null || !existingEnums.Any())
        {
            throw new Exception("No enums found for update.");
        }
        foreach (var dto in dtos)
        {
            if (!existingEnums.TryGetValue(dto.Id, out var updateEnum))
            {
                throw new Exception($"Enum with ID {dto.Id} not found for update.");
            }
            dto.Adapt(updateEnum);
            updateList.Add(updateEnum);
            var saveEnumValues = new List<RlxEnumValue>();
            if (dto.UpdateValues != null)
            {
                var updateEnumValues = await UpdateEnumValues(dto.UpdateValues, false);
                saveEnumValues.AddRange(updateEnumValues);
            }
            if (dto.AddValues != null)
            {
                var addEnumValues = await AddEnumValues(dto.AddValues, false);
                saveEnumValues.AddRange(addEnumValues);
            }
            if (saveEnumValues.Any())
            {
                updateEnum.RlxEnumValues = saveEnumValues.ToList();
            }
        }
        return await _rlxEnumStore.UpdateEnumsAsync(updateList, saveChanges);
    }
    public async Task<IEnumerable<RlxEnumValue>> AddEnumValues(IEnumerable<RlxEnumValueAddDto> dtos, bool saveChanges = false)
    {
        if (dtos == null || !dtos.Any())
            return await Task.FromResult(Enumerable.Empty<RlxEnumValue>());
        var addList = new List<RlxEnumValue>();
        foreach (var dto in dtos)
        {
            var addEnum = dto.Adapt<RlxEnumValue>();
            addList.Add(addEnum);
        }
        return await _rlxEnumStore.AddEnumValuesAsync(addList, saveChanges);
    }
    public async Task<IEnumerable<RlxEnumValue>> UpdateEnumValues(IEnumerable<RlxEnumValueUpdateDto> dtos, bool saveChanges = false)
    {
        if (dtos == null || !dtos.Any())
            return await Task.FromResult(Enumerable.Empty<RlxEnumValue>());
        var ids = dtos.Select(x => x.Id).Where(x => x != null).ToArray();
        var existingEnumValues = (await _rlxEnumStore.GetEnumValuesForUpdateAsync(ids)).ToDictionary(x => x.Id);
        var updateList = new List<RlxEnumValue>();
        if (existingEnumValues == null || !existingEnumValues.Any())
        {
            throw new Exception("No enum values found for update.");
        }
        foreach (var dto in dtos)
        {
            if (!existingEnumValues.TryGetValue(dto.Id, out var updateEnumValue))
            {
                throw new Exception($"Enum with ID {dto.Id} not found for update.");
            }
            dto.Adapt(updateEnumValue);
            updateList.Add(updateEnumValue);
        }
        return await _rlxEnumStore.UpdateEnumValuesAsync(updateList, saveChanges);
    }
    public Task<IEnumerable<RlxEnumForViewDto>> GetActiveEnums(IEnumerable<string> ids)
    {
        if (ids == null || !ids.Any())
            return Task.FromResult(Enumerable.Empty<RlxEnumForViewDto>());
        return _rlxEnumStore.GetActiveEnums(ids);
    }
    public async Task<IDictionary<string, int>> IdConvertForEnumValue(IEnumerable<string> enumValueIds)
    {
        if (enumValueIds == null || !enumValueIds.Any())
            return new Dictionary<string, int>();
        return await _rlxEnumStore.IdConvertForEnumValue(enumValueIds);
    }
    public async Task SaveEnumsAsync(IEnumerable<RlxEnumSaveDto> dtos)
    {
        var eAddList = new List<RlxEnum>();
        var eUpdateList = new List<RlxEnum>();
        var savedEnums = Enumerable.Empty<RlxEnum>();
        var savedEnumValues = Enumerable.Empty<RlxEnumValue>();
        var eids = dtos.Where(x => x.Id != null).Select(x => x.Id!).ToArray();
        if (eids.Length > 0)
            savedEnums = await _rlxEnumStore.GetEnumsForUpdateAsync(eids);
        var evids = dtos.SelectMany(x => x.Values?.Where(y => y.Id != null)?.Select(y => y.Id!) ?? Enumerable.Empty<string>()).ToArray();
        if (evids.Length > 0)
            savedEnumValues = await _rlxEnumStore.GetEnumValuesForUpdateAsync(evids);
        foreach (var dto in dtos)
        {
            RlxEnum? saveEnum;
            if (dto.Id == null)
            {
                saveEnum = dto.Adapt<RlxEnum>();
                eAddList.Add(saveEnum);
            }
            else
            {
                saveEnum = savedEnums?.FirstOrDefault(x => x.Id == dto.Id);
                if (saveEnum != null)
                {
                    dto.Adapt(saveEnum);
                    eUpdateList.Add(saveEnum);
                }
            }
            if (dto.Values != null)
            {
                var evAddList = new List<RlxEnumValue>();
                var evUpdateList = new List<RlxEnumValue>();
                foreach (var vDto in dto.Values)
                {
                    RlxEnumValue? saveEnumValue;
                    if (vDto.Id == null)
                    {
                        saveEnumValue = vDto.Adapt<RlxEnumValue>();
                        saveEnumValue.RlxEnum = saveEnum;
                        evAddList.Add(saveEnumValue);
                    }
                    else
                    {
                        saveEnumValue = savedEnumValues?.FirstOrDefault(x => x.Id == vDto.Id);
                        if (saveEnumValue != null)
                        {
                            vDto.Adapt(saveEnumValue);
                            saveEnumValue.RlxEnum = saveEnum;
                            evUpdateList.Add(saveEnumValue);
                        }
                    }
                }
                if (evAddList.Count > 0)
                {
                    await _rlxEnumStore.AddEnumValuesAsync(evAddList, false);
                }
                if (evUpdateList.Count > 0)
                {
                    await _rlxEnumStore.UpdateEnumValuesAsync(evUpdateList);
                }
            }
        }
        if (eAddList.Count > 0)
        {
            await _rlxEnumStore.AddEnumsAsync(eAddList);
        }
        if (eUpdateList.Count > 0)
        {
            await _rlxEnumStore.UpdateEnumsAsync(eUpdateList);
        }
        await _context.SaveChangesAsync();
    }
    public Task<IEnumerable<RlxEnumUpdateDto>> UpdateEnums(IEnumerable<RlxEnumUpdateDto> dtos)
    {
        throw new NotImplementedException();
    }
}