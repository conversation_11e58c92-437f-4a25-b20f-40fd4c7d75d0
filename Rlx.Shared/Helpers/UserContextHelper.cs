using Microsoft.AspNetCore.Http;
using Rlx.Shared.Interfaces;
using System.IdentityModel.Tokens.Jwt;
namespace Rlx.Shared.Helpers;
public class UserContextHelper : IUserContextHelper
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    public UserContextHelper(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }
    public string? GetUserId()
    {
        var handler = new JwtSecurityTokenHandler();
        var token = _httpContextAccessor.HttpContext?.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
        if (string.IsNullOrEmpty(token))
        {
            return null;
        }
        var jwtToken = handler.ReadJwtToken(token);
        return jwtToken.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;
        // return _httpContextAccessor.HttpContext?.Request.Headers["UserId"].FirstOrDefault();
    }

    public string? GetUserIp()
    {
        var ipAddress = _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString();
        return ipAddress;
    }

    public static string? GetUserId(HttpContext context)
    {
        var handler = new JwtSecurityTokenHandler();
        var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
        if (string.IsNullOrEmpty(token))
        {
            return null;
        }
        var jwtToken = handler.ReadJwtToken(token);
        return jwtToken.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;
    }
}