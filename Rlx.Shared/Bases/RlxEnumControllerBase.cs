using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Rlx.Shared.DbContexts;
using Rlx.Shared.Interfaces;
[ApiController]
public abstract class RlxEnumControllerBase<TContext> : ControllerBase where TContext : RlxEnumDbContext
{
    protected readonly TContext _dbContext;
    protected readonly IRlxEnumManager<TContext> _rlxEnumManager;
    protected ILogger<RlxEnumControllerBase<TContext>> _logger;
    public RlxEnumControllerBase(TContext dbContext, IRlxEnumManager<TContext> rlxEnumManager, ILogger<RlxEnumControllerBase<TContext>> logger)
    {
        _dbContext = dbContext;
        _rlxEnumManager = rlxEnumManager;
        _logger = logger;
    }
    public virtual async Task<IActionResult> GetActiveEnums(IEnumerable<string> ids)
    {
        if (ids == null || !ids.Any())
        {
            return BadRequest("No IDs provided");
        }
        // todo: cache yapilacak
        var data = await _rlxEnumManager.GetActiveEnums(ids);
        return Ok(data);
    }
}