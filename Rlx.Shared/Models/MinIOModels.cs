using System.ComponentModel.DataAnnotations;
namespace Rlx.Shared.Models;
/// <summary>
/// MinIO object storage konfigürasyon modeli
/// </summary>
public class MinIOConfiguration
{
    [Required]
    public string Name { get; set; } = string.Empty;
    /// <summary>
    /// MinIO server endpoint (örn: localhost:9000)
    /// </summary>
    [Required]
    public string Endpoint { get; set; } = string.Empty;
    /// <summary>
    /// MinIO access key
    /// </summary>
    [Required]
    public string AccessKey { get; set; } = string.Empty;
    /// <summary>
    /// MinIO secret key
    /// </summary>
    [Required]
    public string SecretKey { get; set; } = string.Empty;
    /// <summary>
    /// SSL kullanımı (production'da true olmalı)
    /// </summary>
    public bool UseSSL { get; set; } = false;
    /// <summary>
    /// Varsayılan bucket adı
    /// </summary>
    [Required]
    public string DefaultBucket { get; set; } = "default-bucket";
    /// <summary>
    /// Maksimum dosya boyutu (bytes) - varsayılan 10MB
    /// </summary>
    [Range(1, long.MaxValue)]
    public long MaxFileSize { get; set; } = 10 * 1024 * 1024; // 10MB
    /// <summary>
    /// İzin verilen dosya uzantıları
    /// </summary>
    public List<string> AllowedExtensions { get; set; } = new()
    {
        ".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png"
    };
    /// <summary>
    /// İzin verilen MIME türleri
    /// </summary>
    public List<string> AllowedMimeTypes { get; set; } = new()
    {
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "image/jpeg",
        "image/png"
    };
    /// <summary>
    /// Presigned URL geçerlilik süresi (saniye) - varsayılan 1 saat
    /// </summary>
    [Range(60, 86400)] // 1 dakika ile 24 saat arası
    public int PresignedUrlExpirySeconds { get; set; } = 3600; // 1 saat
    /// <summary>
    /// Bucket region (opsiyonel)
    /// </summary>
    public string? Region { get; set; }
    /// <summary>
    /// Connection timeout (saniye)
    /// </summary>
    [Range(1, 300)]
    public int ConnectionTimeoutSeconds { get; set; } = 30;
    /// <summary>
    /// Konfigürasyonun geçerli olup olmadığını kontrol eder
    /// </summary>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(Endpoint) &&
               !string.IsNullOrWhiteSpace(AccessKey) &&
               !string.IsNullOrWhiteSpace(SecretKey) &&
               !string.IsNullOrWhiteSpace(DefaultBucket) &&
               MaxFileSize > 0 &&
               AllowedExtensions.Count > 0 &&
               AllowedMimeTypes.Count > 0;
    }
    /// <summary>
    /// Dosya uzantısının izin verilen listede olup olmadığını kontrol eder
    /// </summary>
    public bool IsExtensionAllowed(string extension)
    {
        return AllowedExtensions.Contains(extension.ToLowerInvariant());
    }
    /// <summary>
    /// MIME türünün izin verilen listede olup olmadığını kontrol eder
    /// </summary>
    public bool IsMimeTypeAllowed(string mimeType)
    {
        return AllowedMimeTypes.Contains(mimeType.ToLowerInvariant());
    }
}
/// <summary>
/// MinIO upload sonucu
/// </summary>
public class MinIOUploadResult
{
    public bool Success { get; set; }
    public string? ObjectName { get; set; }
    public string? ETag { get; set; }
    public long Size { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UploadedAt { get; set; } = DateTime.UtcNow;
}
/// <summary>
/// MinIO dosya metadata'sı
/// </summary>
public class MinIOFileMetadata
{
    public string ObjectName { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public long Size { get; set; }
    public string ContentType { get; set; } = string.Empty;
    public string? ETag { get; set; }
    public string? Checksum { get; set; }
    public DateTime LastModified { get; set; }
    public Dictionary<string, string> UserMetadata { get; set; } = new();
}
/// <summary>
/// MinIO dosya bilgisi
/// </summary>
public class MinIOFileInfo
{
    public string ObjectName { get; set; } = string.Empty;
    public long Size { get; set; }
    public DateTime LastModified { get; set; }
    public string? ETag { get; set; }
    public bool IsDirectory { get; set; }
}
/// <summary>
/// MinIO dosya validation sonucu
/// </summary>
public class MinIOValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public string? DetectedContentType { get; set; }
    public long FileSize { get; set; }
}
