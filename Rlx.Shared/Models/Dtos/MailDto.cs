using System.ComponentModel.DataAnnotations;
namespace Rlx.Shared.Models.Dtos;
public class MailDto
{
    [Required]
    public List<string> Recipients { get; set; } = new List<string>();
    [Required]
    public string MailName { get; set; } = string.Empty;
    [Required]
    public string Body { get; set; } = string.Empty;
    [Required]
    public string Subject { get; set; } = string.Empty;
    public bool IsHtml { get; set; }
}
