using System.ComponentModel.DataAnnotations;
namespace Rlx.Shared.Models;
public class MailConfig
{
    [Required]
    public string Name { get; set; } = string.Empty;
    [Required]
    public string SmtpServer { get; set; } = string.Empty;
    [Required]
    public int SmtpPort { get; set; }
    [Required]
    public string Username { get; set; } = string.Empty;
    [Required]
    public string Password { get; set; } = string.Empty;
    [Required]
    public string FromEmail { get; set; } = string.Empty;
    [Required]
    public string FromName { get; set; } = string.Empty;
    public bool UseSsl { get; set; } = true;
}
