---
type: "always_apply"
---

# AcademicPerformance API Cursor Rules

## Proje Genel Bilgileri

Bu proje, akademik performans yönetim sistemi için geliştirilmiş bir .NET Core Web API projesidir.

### Teknoloji Stack

- .NET 8.0
- ASP.NET Core Web API
- Entity Framework Core (PostgreSQL)
- MongoDB (dinamik veriler için)
- Rlx.Shared kütüphanesi (ortak bileşenler)
- MinIO (dosya depolama)
- Mapster (object mapping)
- Swagger/OpenAPI

### Proje Yapısı

```
AcademicPerformance/
├── Controllers/           # API endpoint'leri
├── Managers/             # İş mantığı katmanı
├── Stores/               # Veri erişim katmanı
├── Services/             # Özel servisler
├── Models/               # Entity ve DTO'lar
├── DbContexts/           # EF Core context'leri
├── Interfaces/           # Interface tanımları
├── Configurations/       # Yapılandırma dosyaları
└── Resources/            # Lokalizasyon kaynakları
```

## Kodlama Standartları

### Controller Kuralları

- Tüm controller'lar `BaseApiController`'dan miras almalı
- Route pattern: `[Route("[controller]/[action]")]`
- Authorization: `APConsts.Policies` kullan
- Response format: `SuccessResponse()`, `ErrorResponse()` metodları
- Logging: `IRlxSystemLogHelper` kullan
- Localization: `IStringLocalizer<SharedResource>` kullan

### Endpoint Örnekleri

```csharp
[HttpGet]
[Authorize(APConsts.Policies.ViewData)]
public async Task<IActionResult> GetData([FromQuery] PagedListCo<GetDataCo> co)
{
    try
    {
        var userId = _userContextHelper.GetUserId();
        await _systemLogHelper.LogInfoAsync($"User {userId} requested data");
        var result = await _manager.GetDataAsync(co);
        return SuccessResponse(result);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error getting data");
        return HandleException(ex);
    }
}
```

### Manager-Store Pattern

- Controller → Manager → Store yapısını takip et
- Manager: İş mantığı ve validasyon
- Store: Sadece veri erişimi
- Dependency injection kullan

### Response Format

Tüm API response'ları standart format kullanmalı:

```json
{
  "status": "Success",
  "message": "İşlem başarılı",
  "data": { ... },
  "meta": { ... }
}
```

### Pagination

- GET endpoint'lerinde pagination zorunlu
- `PagedListCo<T>` ve `PagedListDto<T>` kullan
- Rlx.Shared pagination yapılarını tercih et

## Mevcut Controller'lar ve Endpoint'ler

### UserController

- `GET /User/GetProfile` - Kullanıcı profil bilgileri
- `GET /User/GetTestProfile` - Test endpoint'i
- `GET /User/GetUserRoles` - Kullanıcı rolleri
- `GET /User/GetUserPermissions` - Kullanıcı yetkileri

### CriteriaController (15 endpoint)

- `GET /Criteria/GetDynamicTemplates` - Dinamik kriter şablonları
- `POST /Criteria/CreateDynamicTemplate` - Dinamik kriter oluştur
- `PUT /Criteria/UpdateDynamicTemplate` - Dinamik kriter güncelle
- `DELETE /Criteria/DeleteDynamicTemplate` - Dinamik kriter sil

### FormController (21 endpoint)

- `GET /Form/GetForms` - Form listesi (paginated)
- `GET /Form/GetFormById` - Form detayı
- `POST /Form/CreateForm` - Form oluştur
- `PUT /Form/UpdateForm` - Form güncelle
- `DELETE /Form/DeleteForm` - Form sil

### AcademicianController (23 endpoint)

- `GET /Academician/GetDashboard` - Akademisyen dashboard
- `GET /Academician/GetSubmissions` - Başvuru listesi
- `POST /Academician/SubmitData` - Veri gönder

### StaticCriterionDataController (5 endpoint)

- `GET /StaticCriterionData/GetAcademicianStaticCriteria` - Statik kriterler
- `POST /StaticCriterionData/GetSpecificStaticCriteria` - Belirli kriterler
- `GET /StaticCriterionData/CheckHealth` - Sağlık kontrolü

## Authorization Policies

```csharp
// APConsts.Policies'den kullan - Policy-based authorization
APConsts.Policies.AccessAP        // Temel erişim
APConsts.Policies.ViewData        // Veri görüntüleme
APConsts.Policies.EditData        // Veri düzenleme
APConsts.Policies.AllAccess       // Tam erişim
APConsts.Policies.ManageCriteria  // Kriter yönetimi
APConsts.Policies.SubmitData      // Veri gönderme
APConsts.Policies.Test            // Test endpoint'leri

// Authorization Service kullanımı
IAPAuthorizationService _authService;
var isAdmin = _authService.IsAdmin(User);
var hasPermission = _authService.HasPermission(User, "permission.action.ap", "managecriteria");
```

## Database Context

- `AcademicPerformanceDbContext` - Ana PostgreSQL context
- `AcademicPerformanceLocalizationDbContext` - Lokalizasyon
- Migration'lar otomatik çalışır (Program.cs)

## Logging ve Error Handling

```csharp
// Logging
await _systemLogHelper.LogInfoAsync("İşlem başarılı");
await _systemLogHelper.LogErrorAsync("Hata oluştu", ex);
// Error handling
try
{
    // İşlem
}
catch (Exception ex)
{
    _logger.LogError(ex, "Hata mesajı");
    return HandleException(ex, "Özel hata mesajı");
}
```

## Localization

- Türkçe ve İngilizce destekli
- `SharedResource.resx` ve `SharedResource.tr.resx`
- Controller response'larında localize mesajlar kullan
- Log mesajları localize edilmez

## Test Endpoint'leri

Mevcut test endpoint'leri:

- `GET /User/GetTestProfile` - Mock kullanıcı profili
- `GET /StaticCriterionData/CheckHealth` - Servis sağlık kontrolü

## File Upload ve MinIO

```csharp
// FileUploadController kullanımı
[HttpPost]
public async Task<IActionResult> UploadFile(IFormFile file)
{
    // MinIO service kullan
    var result = await _minIOFileService.UploadFileAsync(file);
    return SuccessResponse(result);
}
```

## MongoDB Integration

- Dinamik veriler için MongoDB kullanılır
- `appsettings.json`'da `apdydynamicdata` database adı
- Connection string: `MongoDbSettings:ConnectionString`

## Docker Compose Kullanımı

```bash
# Veritabanlarını başlat
docker compose up -d postgres mongodb
# Uygulamayı çalıştır
dotnet run
# Migration çalıştır (gerekirse)
dotnet ef database update
```

## API Test Örnekleri

```bash
# Kullanıcı profili test
curl -X GET "https://localhost:7001/User/GetProfile" \
  -H "Authorization: Bearer {token}"
# Form listesi (paginated)
curl -X GET "https://localhost:7001/Form/GetForms?PageNumber=1&PageSize=10"
# Sağlık kontrolü
curl -X GET "https://localhost:7001/StaticCriterionData/CheckHealth"
```

## Entity Framework Patterns

```csharp
// Entity base model (Rlx.Shared'den)
public class MyEntity : EntityBaseModel
{
    // string Id (GUID)
    // int AutoIncrementId
    // DateTime CreatedAt, UpdatedAt
    // string CreatedBy, UpdatedBy
}
// DbContext kullanımı
public class MyStore : IMyStore
{
    private readonly AcademicPerformanceDbContext _context;
    public async Task<PagedListDto<T>> GetPagedAsync<T>(PagedListCo<T> co)
    {
        // Pagination logic
    }
}
```

## Önemli Notlar

- Test endpoint'i oluşturma, mevcut endpoint'leri kullan
- Hata durumunda dosyayı silme, düzeltmeye odaklan
- Curl ile test yapılabilir
- Docker compose ile PostgreSQL ve MongoDB çalışır
- Akademisyen bilgileri ilk seferde OrganizationManagement API'den alınır
- Sonraki isteklerde kendi DB'den okunur (cache pattern)
- GET servislerinde pagination zorunlu
- Kırıcı değişiklikler (breaking changes) kabul edilemez
- Cevaplarını türkçe ver
- Anlamlandırılamayan yerde /Users/<USER>/Desktop/arelproject/acperform/prompt klasörü içerisindeki dökümanları analiz et

## Authorization System Migration (2024)

- ❌ APRoleClaimsTransformation.cs kaldırıldı (150+ satır karmaşık kod)
- ✅ Rlx.Shared RoleClaimsTransformation kullanılıyor
- ✅ APAuthorizationService ile policy-based authorization
- ✅ Hardcoded super admin listesi ve claim injection kaldırıldı
- ✅ PolicyConfig.cs ile 42+ policy tanımı
- 📚 Detaylar: `docs/AUTHORIZATION_MIGRATION.md`
